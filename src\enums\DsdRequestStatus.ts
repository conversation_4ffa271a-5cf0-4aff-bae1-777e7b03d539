export enum DsdRequestStatus {
  DRAFT = 0,
  SERVICE_FORM_PENDING = 1,
  MANAGER_APPROVAL_PENDING = 2,
  RL_APPROVAL_PENDING = 3,
  PD_APPROVAL_PENDING = 4,
  SERVICE_APPROVAL_PENDING = 5,
  APPROVED = 6,
  STATUS_SERVICE_FORM_APPROVED = 7,
  APPROVE_PRICING_APPROVED = 8,
  APPROVAL_PRICING_PENDING=9
}

export function getDsdRequestStatusText(status: DsdRequestStatus | number): string {
  switch (status) {
    case DsdRequestStatus.DRAFT:
      return 'Draft';
    case DsdRequestStatus.SERVICE_FORM_PENDING:
      return 'Service Form Pending';
    case DsdRequestStatus.MANAGER_APPROVAL_PENDING:
      return 'Manager Approval Pending';
    case DsdRequestStatus.RL_APPROVAL_PENDING:
      return 'RL Approval Pending';
    case DsdRequestStatus.PD_APPROVAL_PENDING:
      return 'PD Approval Pending';
    case DsdRequestStatus.SERVICE_APPROVAL_PENDING:
      return 'Service Approval Pending';
    case DsdRequestStatus.APPROVED:
      return 'Approved';
    case DsdRequestStatus.STATUS_SERVICE_FORM_APPROVED:
      return 'Service Form Approved';
    case DsdRequestStatus.APPROVE_PRICING_APPROVED:
      return 'Approve Pricing Approved';
    case DsdRequestStatus.APPROVAL_PRICING_PENDING:
      return 'Approve Pricing Pending';
    default:
      return 'Unknown';
  }
}
