<template>
	<v-container fluid class="pa-4">
		<!-- <PERSON>er -->
		<v-row class="mb-4">
			<v-col cols="12" class="d-flex justify-space-between align-center">
				<div>
					<h1 class="text-h4 font-weight-bold mb-2">{{ t('Dashboard') }}</h1>
					<p class="text-subtitle-1 text-medium-emphasis">{{ t('Channel Support DSD - Request Management Overview') }}</p>
				</div>
				<v-menu>
					<template v-slot:activator="{ props }">
						<v-btn
							v-bind="props"
							color="primary"
							variant="elevated"
							size="default"
							prepend-icon="description"
							append-icon="expand_more"
							class="ml-4"
						>
							Generate Reports
						</v-btn>
					</template>
					<v-list density="compact">
						<v-list-item
							v-for="report in reportOptions"
							:key="report.value"
							@click="generateReport(report.value)"
							:prepend-icon="report.icon"
						>
							<v-list-item-title>{{ report.label }}</v-list-item-title>
							<v-list-item-subtitle>{{ report.description }}</v-list-item-subtitle>
						</v-list-item>
					</v-list>
				</v-menu>
			</v-col>
		</v-row>

		<!-- Workflow Status Cards -->
		<v-row class="mb-6">
			<v-col cols="12" sm="6" md="4" lg="2">
				<v-card class="pa-3 text-center" color="grey-lighten-1" variant="tonal">
					<v-icon size="32" class="mb-2">edit</v-icon>
					<div class="text-h6 font-weight-bold">{{ workflowStats.draft }}</div>
					<div class="text-caption">Draft</div>
				</v-card>
			</v-col>
			<v-col cols="12" sm="6" md="4" lg="2">
				<v-card class="pa-3 text-center" color="orange-lighten-1" variant="tonal">
					<v-icon size="32" class="mb-2">assignment</v-icon>
					<div class="text-h6 font-weight-bold">{{ workflowStats.serviceFormPending }}</div>
					<div class="text-caption">Service Form</div>
				</v-card>
			</v-col>
			<v-col cols="12" sm="6" md="4" lg="2">
				<v-card class="pa-3 text-center" color="blue-lighten-1" variant="tonal">
					<v-icon size="32" class="mb-2">supervisor_account</v-icon>
					<div class="text-h6 font-weight-bold">{{ workflowStats.managerApproval }}</div>
					<div class="text-caption">Manager</div>
				</v-card>
			</v-col>
			<v-col cols="12" sm="6" md="4" lg="2">
				<v-card class="pa-3 text-center" color="purple-lighten-1" variant="tonal">
					<v-icon size="32" class="mb-2">account_tree</v-icon>
					<div class="text-h6 font-weight-bold">{{ workflowStats.rlApproval }}</div>
					<div class="text-caption">Regional Lead</div>
				</v-card>
			</v-col>
			<v-col cols="12" sm="6" md="4" lg="2">
				<v-card class="pa-3 text-center" color="indigo-lighten-1" variant="tonal">
					<v-icon size="32" class="mb-2">attach_money</v-icon>
					<div class="text-h6 font-weight-bold">{{ workflowStats.pdApproval }}</div>
					<div class="text-caption">Price Desk</div>
				</v-card>
			</v-col>
			<v-col cols="12" sm="6" md="4" lg="2">
				<v-card class="pa-3 text-center" color="success" variant="tonal">
					<v-icon size="32" class="mb-2">check_circle</v-icon>
					<div class="text-h6 font-weight-bold">{{ workflowStats.approved }}</div>
					<div class="text-caption">Approved</div>
				</v-card>
			</v-col>
		</v-row>

		<!-- Main Content Row -->
		<v-row>
			<!-- Status Visualization & Recent Activity -->
			<v-col cols="12" lg="8">
				<!-- Status Distribution Chart -->
				<v-card class="mb-4">
					<v-card-title class="d-flex align-center justify-space-between">
						<div class="d-flex align-center">
							<v-icon class="mr-2">pie_chart</v-icon>
							Request Status Distribution
						</div>
						<v-select
							v-model="selectedTimeframe"
							:items="timeframeOptions"
							item-title="label"
							item-value="value"
							density="compact"
							variant="outlined"
							hide-details
							style="min-width: 140px; max-width: 180px;"
							class="ml-4"
						></v-select>
					</v-card-title>
					<v-card-text>
						<div class="d-flex justify-center align-center" style="min-height: 300px;">
							<div class="chart-container" style="position: relative; width: 280px; height: 280px;">
								<!-- Custom Pie Chart using CSS -->
								<div class="pie-chart" :style="pieChartStyle">
									<div class="pie-center">
										<div class="text-h4 font-weight-bold">{{ totalRequests }}</div>
										<div class="text-body-2">Total Requests</div>
									</div>
								</div>
								<!-- Legend -->
								<div class="chart-legend mt-4">
									<div class="d-flex flex-wrap justify-center gap-3">
										<div v-for="(item, index) in chartData" :key="index" class="legend-item d-flex align-center">
											<div class="legend-color" :style="{ backgroundColor: item.color }"></div>
											<span class="text-body-2 ml-2">{{ item.label }} ({{ item.value }})</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</v-card-text>
				</v-card>

				<!-- Recent Activity -->
				<v-card class="mb-4">
					<v-card-title class="d-flex align-center">
						<v-icon class="mr-2">history</v-icon>
						Recent Activity
					</v-card-title>
					<v-card-text>
						<v-list v-if="recentActivity.length > 0">
							<v-list-item
								v-for="(activity, index) in recentActivity"
								:key="index"
								class="px-0"
							>
								<template v-slot:prepend>
									<v-avatar :color="getStatusColor(activity.status)" size="small">
										<v-icon size="16" color="white">{{ getStatusIcon(activity.status) }}</v-icon>
									</v-avatar>
								</template>
								<v-list-item-title>{{ activity.title }}</v-list-item-title>
								<v-list-item-subtitle>
									{{ activity.description }} • {{ formatDate(activity.date) }}
								</v-list-item-subtitle>
								<template v-slot:append>
									<v-chip :color="getStatusColor(activity.status)" size="small" variant="tonal">
										{{ getDsdRequestStatusText(activity.status) }}
									</v-chip>
								</template>
							</v-list-item>
						</v-list>
						<div v-else class="text-center py-8 text-medium-emphasis">
							<v-icon size="64" class="mb-4">inbox</v-icon>
							<div>No recent activity</div>
						</div>
					</v-card-text>
				</v-card>
			</v-col>

			<!-- Quick Actions & Status Overview -->
			<v-col cols="12" lg="4">
				<!-- Quick Actions -->
				<v-card class="mb-4">
					<v-card-title class="d-flex align-center">
						<v-icon class="mr-2">flash_on</v-icon>
						Quick Actions
					</v-card-title>
					<v-card-text>
						<v-btn
							block
							color="primary"
							class="mb-3"
							prepend-icon="add"
							:to="{ name: 'pageNewSalesRequest' }"
						>
							New Sales Request
						</v-btn>
						<v-btn
							block
							color="secondary"
							class="mb-3"
							prepend-icon="list"
							:to="{ name: 'mySalesRequests' }"
						>
							My Requests
						</v-btn>
						<v-btn
							block
							variant="outlined"
							prepend-icon="attach_money"
							:to="{ name: 'pagePriceDesk' }"
							v-if="userInternalStore.roles?.includes(UserRole.SERVICE_DESK_ANALYST)"
						>
							Price Desk
						</v-btn>
					</v-card-text>
				</v-card>

				<!-- Status Distribution -->
				<v-card>
					<v-card-title class="d-flex align-center">
						<v-icon class="mr-2">pie_chart</v-icon>
						Request Status Distribution
					</v-card-title>
					<v-card-text>
						<div v-for="status in statusDistribution" :key="status.name" class="mb-3">
							<div class="d-flex justify-space-between align-center mb-1">
								<span class="text-body-2">{{ status.name }}</span>
								<span class="text-body-2 font-weight-bold">{{ status.count }}</span>
							</div>
							<v-progress-linear
								:model-value="status.percentage"
								:color="status.color"
								height="8"
								rounded
							></v-progress-linear>
						</div>
					</v-card-text>
				</v-card>
			</v-col>
		</v-row>

		<!-- Performance Metrics -->
		<v-row class="mt-4">
			<v-col cols="12">
				<v-card>
					<v-card-title class="d-flex align-center">
						<v-icon class="mr-2">trending_up</v-icon>
						Performance Overview
					</v-card-title>
					<v-card-text>
						<v-row>
							<v-col cols="12" md="4">
								<div class="text-center">
									<div class="text-h6 text-success">{{ metrics.avgProcessingTime }}</div>
									<div class="text-body-2 text-medium-emphasis">Avg Processing Time</div>
								</div>
							</v-col>
							<v-col cols="12" md="4">
								<div class="text-center">
									<div class="text-h6 text-primary">{{ metrics.approvalRate }}%</div>
									<div class="text-body-2 text-medium-emphasis">Approval Rate</div>
								</div>
							</v-col>
							<v-col cols="12" md="4">
								<div class="text-center">
									<div class="text-h6 text-info">{{ metrics.monthlyVolume }}</div>
									<div class="text-body-2 text-medium-emphasis">This Month</div>
								</div>
							</v-col>
						</v-row>
					</v-card-text>
				</v-card>
			</v-col>
		</v-row>
	</v-container>
</template>

<script setup lang="ts">
/**
 * @file Dashboard page.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> AI Assistant
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAppStore } from '@/stores/AppStore';
import { useUserStore } from '@/stores/UserStore';
import { useInternalUserStore } from '@/stores/InternalUserStore';
import { useI18n } from 'vue-i18n';
import { DsdRequestStatus, getDsdRequestStatusText } from '@/enums/DsdRequestStatus';
import { UserRole } from '@/lib/common/types';

/**
 * ----
 * Main
 * ----
 */

// Add stores and router
const appStore = useAppStore();
const userStore = useUserStore();
const userInternalStore = useInternalUserStore();
const router = useRouter();

// Add language support
const { t } = useI18n();

// Reactive data - Workflow-based statistics
const workflowStats = ref({
	draft: 0,
	serviceFormPending: 0,
	managerApproval: 0,
	rlApproval: 0,
	pdApproval: 0,
	serviceApproval: 0,
	approved: 0
});

// Timeline filter data
const selectedTimeframe = ref('total');
const timeframeOptions = ref([
	{ label: 'Total', value: 'total' },
	{ label: 'This Week', value: 'week' },
	{ label: 'This Month', value: 'month' },
	{ label: 'This Quarter', value: 'quarter' },
	{ label: 'This Year', value: 'year' }
]);

// Report generation options
const reportOptions = ref([
	{
		label: 'Status Summary Report',
		value: 'status_summary',
		description: 'Overview of request statuses',
		icon: 'assessment'
	},
	// {
	// 	label: 'Performance Analytics',
	// 	value: 'performance',
	// 	description: 'Approval times and bottlenecks',
	// 	icon: 'trending_up'
	// },
	// {
	// 	label: 'Workflow Efficiency',
	// 	value: 'workflow',
	// 	description: 'Process optimization insights',
	// 	icon: 'timeline'
	// },
	// {
	// 	label: 'Monthly Summary',
	// 	value: 'monthly',
	// 	description: 'Comprehensive monthly report',
	// 	icon: 'calendar_month'
	// },
	// {
	// 	label: 'Export Data (CSV)',
	// 	value: 'csv_export',
	// 	description: 'Download raw data',
	// 	icon: 'download'
	// }
]);

const recentActivity = ref([
	{
		title: 'Sales Request #SR-2024-001',
		description: 'Hardware request for Canon imageRUNNER ADVANCE',
		status: DsdRequestStatus.MANAGER_APPROVAL_PENDING,
		date: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
	},
	{
		title: 'Sales Request #SR-2024-002',
		description: 'Software licensing request',
		status: DsdRequestStatus.APPROVED,
		date: new Date(Date.now() - 5 * 60 * 60 * 1000) // 5 hours ago
	},
	{
		title: 'Sales Request #SR-2024-003',
		description: 'Service contract renewal',
		status: DsdRequestStatus.SERVICE_FORM_PENDING,
		date: new Date(Date.now() - 24 * 60 * 60 * 1000) // 1 day ago
	}
]);

const metrics = ref({
	avgProcessingTime: '3.2 days',
	approvalRate: 87,
	monthlyVolume: 42
});

// Computed properties
const totalRequests = computed(() => {
	return Object.values(filteredWorkflowStats.value).reduce((sum, count) => sum + count, 0);
});

// Filtered workflow stats based on selected timeframe
const filteredWorkflowStats = computed(() => {
	// In a real application, this would filter data based on the selected timeframe
	// For now, we'll simulate different data for different timeframes
	switch (selectedTimeframe.value) {
		case 'week':
			return {
				draft: Math.floor(workflowStats.value.draft * 0.3),
				serviceFormPending: Math.floor(workflowStats.value.serviceFormPending * 0.4),
				managerApproval: Math.floor(workflowStats.value.managerApproval * 0.5),
				rlApproval: Math.floor(workflowStats.value.rlApproval * 0.3),
				pdApproval: Math.floor(workflowStats.value.pdApproval * 0.2),
				serviceApproval: Math.floor(workflowStats.value.serviceApproval * 0.4),
				approved: Math.floor(workflowStats.value.approved * 0.2)
			};
		case 'month':
			return {
				draft: Math.floor(workflowStats.value.draft * 0.7),
				serviceFormPending: Math.floor(workflowStats.value.serviceFormPending * 0.8),
				managerApproval: Math.floor(workflowStats.value.managerApproval * 0.9),
				rlApproval: Math.floor(workflowStats.value.rlApproval * 0.7),
				pdApproval: Math.floor(workflowStats.value.pdApproval * 0.6),
				serviceApproval: Math.floor(workflowStats.value.serviceApproval * 0.8),
				approved: Math.floor(workflowStats.value.approved * 0.6)
			};
		case 'quarter':
			return {
				draft: Math.floor(workflowStats.value.draft * 0.9),
				serviceFormPending: Math.floor(workflowStats.value.serviceFormPending * 0.95),
				managerApproval: workflowStats.value.managerApproval,
				rlApproval: Math.floor(workflowStats.value.rlApproval * 0.9),
				pdApproval: Math.floor(workflowStats.value.pdApproval * 0.8),
				serviceApproval: workflowStats.value.serviceApproval,
				approved: Math.floor(workflowStats.value.approved * 0.8)
			};
		case 'year':
			return {
				draft: workflowStats.value.draft + 5,
				serviceFormPending: workflowStats.value.serviceFormPending + 3,
				managerApproval: workflowStats.value.managerApproval + 8,
				rlApproval: workflowStats.value.rlApproval + 2,
				pdApproval: workflowStats.value.pdApproval + 1,
				serviceApproval: workflowStats.value.serviceApproval + 2,
				approved: workflowStats.value.approved + 15
			};
		default: // 'total'
			return workflowStats.value;
	}
});

// Chart data for pie chart visualization
const chartData = computed(() => {
	return [
		{
			label: 'Draft',
			value: filteredWorkflowStats.value.draft,
			color: '#9E9E9E',
			percentage: totalRequests.value ? (filteredWorkflowStats.value.draft / totalRequests.value) * 100 : 0
		},
		{
			label: 'Service Form',
			value: filteredWorkflowStats.value.serviceFormPending,
			color: '#FF9800',
			percentage: totalRequests.value ? (filteredWorkflowStats.value.serviceFormPending / totalRequests.value) * 100 : 0
		},
		{
			label: 'Manager Approval',
			value: filteredWorkflowStats.value.managerApproval,
			color: '#2196F3',
			percentage: totalRequests.value ? (filteredWorkflowStats.value.managerApproval / totalRequests.value) * 100 : 0
		},
		{
			label: 'Regional Lead',
			value: filteredWorkflowStats.value.rlApproval,
			color: '#9C27B0',
			percentage: totalRequests.value ? (filteredWorkflowStats.value.rlApproval / totalRequests.value) * 100 : 0
		},
		{
			label: 'Price Desk',
			value: filteredWorkflowStats.value.pdApproval,
			color: '#3F51B5',
			percentage: totalRequests.value ? (filteredWorkflowStats.value.pdApproval / totalRequests.value) * 100 : 0
		},
		{
			label: 'Service Approval',
			value: filteredWorkflowStats.value.serviceApproval,
			color: '#607D8B',
			percentage: totalRequests.value ? (filteredWorkflowStats.value.serviceApproval / totalRequests.value) * 100 : 0
		},
		{
			label: 'Approved',
			value: filteredWorkflowStats.value.approved,
			color: '#4CAF50',
			percentage: totalRequests.value ? (filteredWorkflowStats.value.approved / totalRequests.value) * 100 : 0
		}
	].filter(item => item.value > 0); // Only show segments with data
});

// Pie chart CSS styling
const pieChartStyle = computed(() => {
	if (totalRequests.value === 0) {
		return {
			background: '#f5f5f5',
			borderRadius: '50%',
			width: '200px',
			height: '200px',
			position: 'relative' as const,
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center'
		};
	}

	let cumulativePercentage = 0;
	const gradientStops: string[] = [];

	chartData.value.forEach((segment) => {
		const startPercentage = cumulativePercentage;
		const endPercentage = cumulativePercentage + segment.percentage;
		
		gradientStops.push(`${segment.color} ${startPercentage}% ${endPercentage}%`);
		cumulativePercentage = endPercentage;
	});

	return {
		background: `conic-gradient(${gradientStops.join(', ')})`,
		borderRadius: '50%',
		width: '200px',
		height: '200px',
		position: 'relative' as const,
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'center'
	};
});

const statusDistribution = computed(() => {
	const total = totalRequests.value || 1; // Avoid division by zero
	return [
		{
			name: 'Approved',
			count: workflowStats.value.approved,
			percentage: (workflowStats.value.approved / total) * 100,
			color: 'success'
		},
		{
			name: 'Pending Approvals',
			count: workflowStats.value.managerApproval + workflowStats.value.rlApproval + workflowStats.value.pdApproval + workflowStats.value.serviceApproval,
			percentage: ((workflowStats.value.managerApproval + workflowStats.value.rlApproval + workflowStats.value.pdApproval + workflowStats.value.serviceApproval) / total) * 100,
			color: 'warning'
		},
		{
			name: 'In Progress',
			count: workflowStats.value.draft + workflowStats.value.serviceFormPending,
			percentage: ((workflowStats.value.draft + workflowStats.value.serviceFormPending) / total) * 100,
			color: 'info'
		}
	];
});

// Methods
const getStatusColor = (status: DsdRequestStatus): string => {
	switch (status) {
		case DsdRequestStatus.APPROVED:
			return 'success';
		case DsdRequestStatus.DRAFT:
			return 'info';
		case DsdRequestStatus.SERVICE_FORM_PENDING:
		case DsdRequestStatus.MANAGER_APPROVAL_PENDING:
		case DsdRequestStatus.RL_APPROVAL_PENDING:
		case DsdRequestStatus.PD_APPROVAL_PENDING:
		case DsdRequestStatus.SERVICE_APPROVAL_PENDING:
			return 'warning';
		default:
			return 'grey';
	}
};

const getStatusIcon = (status: DsdRequestStatus): string => {
	switch (status) {
		case DsdRequestStatus.APPROVED:
			return 'check';
		case DsdRequestStatus.DRAFT:
			return 'edit';
		case DsdRequestStatus.SERVICE_FORM_PENDING:
		case DsdRequestStatus.MANAGER_APPROVAL_PENDING:
		case DsdRequestStatus.RL_APPROVAL_PENDING:
		case DsdRequestStatus.PD_APPROVAL_PENDING:
		case DsdRequestStatus.SERVICE_APPROVAL_PENDING:
			return 'schedule';
		default:
			return 'help';
	}
};

const formatDate = (date: Date): string => {
	const now = new Date();
	const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
	
	if (diffInHours < 1) {
		return 'Just now';
	} else if (diffInHours < 24) {
		return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
	} else {
		const diffInDays = Math.floor(diffInHours / 24);
		return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
	}
};

// Report generation method
const generateReport = (reportType: string) => {
	// In a real application, this would trigger report generation
	// For now, we'll show a notification and simulate the action
	switch (reportType) {
		case 'status_summary':
			console.log('Generating Status Summary Report for timeframe:', selectedTimeframe.value);
			// Would typically call API to generate PDF/Excel report
			break;
		case 'performance':
			console.log('Generating Performance Analytics Report');
			// Would analyze approval times, bottlenecks, etc.
			break;
		case 'workflow':
			console.log('Generating Workflow Efficiency Report');
			// Would analyze process optimization opportunities
			break;
		case 'monthly':
			console.log('Generating Monthly Summary Report');
			// Would create comprehensive monthly overview
			break;
		case 'csv_export':
			console.log('Exporting data to CSV');
			// Would download CSV file with current filtered data
			exportToCSV();
			break;
		default:
			console.log('Unknown report type:', reportType);
	}
};

// CSV export functionality
const exportToCSV = () => {
	// Create CSV data based on current chart data
	const csvData = [
		['Status', 'Count', 'Percentage'],
		...chartData.value.map(item => [
			item.label,
			item.value.toString(),
			`${item.percentage.toFixed(1)}%`
		])
	];
	
	// Convert to CSV string
	const csvContent = csvData.map(row => row.join(',')).join('\n');
	
	// Create and download file
	const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
	const link = document.createElement('a');
	const url = URL.createObjectURL(blob);
	link.setAttribute('href', url);
	link.setAttribute('download', `dsd-status-report-${selectedTimeframe.value}-${new Date().toISOString().split('T')[0]}.csv`);
	link.style.visibility = 'hidden';
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
};

const loadDashboardData = async () => {
	// Simulate API call - in a real app, this would fetch from your backend
	try {
		// Mock data representing the DSD workflow stages - replace with actual API calls
		workflowStats.value = {
			draft: 12,
			serviceFormPending: 8,
			managerApproval: 15,
			rlApproval: 6,
			pdApproval: 4,
			serviceApproval: 3,
			approved: 28
		};
	} catch (error) {
		console.error('Error loading dashboard data:', error);
	}
};

// Lifecycle
onMounted(async () => {
	appStore.stopPageLoader();
	await loadDashboardData();
});

// Expose methods to template
defineExpose({
	generateReport,
	exportToCSV
});
</script>

<style lang="scss" scoped>
.v-card {
	transition: transform 0.2s ease-in-out;
	
	&:hover {
		transform: translateY(-2px);
	}
}

.v-progress-linear {
	border-radius: 4px;
}

// Pie Chart Styles
.pie-chart {
	border-radius: 50%;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	margin: 0 auto;
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pie-center {
	position: absolute;
	background: white;
	border-radius: 50%;
	width: 120px;
	height: 120px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	z-index: 1;
}

.chart-legend {
	margin-top: 20px;
}

.legend-item {
	margin: 4px 8px;
}

.legend-color {
	width: 16px;
	height: 16px;
	border-radius: 3px;
	flex-shrink: 0;
}

.chart-container {
	display: flex;
	flex-direction: column;
	align-items: center;
}
</style>
