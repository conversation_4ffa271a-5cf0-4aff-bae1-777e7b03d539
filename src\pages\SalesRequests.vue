<script setup lang="ts">
/**
 * @file Sales Requests page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import DataTable from '@/components/common/DataTable.vue';
import { useAppStore } from '@/stores/AppStore';
import actionPermissionChannel from '@/composables/auth/actionPermissionChannel';
import { Channel_Action, CN_Action, type Employee } from '@/lib/common/types';
import actionPermissions from '@/composables/auth/actionPermission';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();
const router = useRouter();

// Add language support
const { t } = useI18n();
const selectedItemId = ref<number | null>(null);
const props = withDefaults(defineProps<{ mineOnly?: boolean }>(), { mineOnly: false });
// Table data
import { getSalesRequests, getMySalesRequests, type SalesRequestDto } from '@/services/salesRequestService';
import { getDsdRequestStatusText, DsdRequestStatus } from '@/enums/DsdRequestStatus';
import StatusBadge from '@/components/core/StatusBadge.vue';
import ManagerChange from '@/components/sales/ManagerChange.vue';
import PDAnalystChange from '@/components/sales/PDAnalystChange.vue';

const loading = ref(false);
const showManagerSearchDialog = ref(false);
const showPDAnalystSearchDialog = ref(false);

const error = ref('');
const headers = ref([
    { title: t('page.sales_requests.table.header.id'), key: 'requestNumber' },
    { title: t('page.sales_requests.table.header.customer'), key: 'customerId' },
    // { title: t('page.sales_requests.table.header.product'), key: 'product' },
    { title: t('page.sales_requests.table.header.status'), key: 'dsdRequestStatus' },
    { title: t('page.sales_requests.table.header.createdBy'), key: 'createdByName' },
    { title: t('page.sales_requests.table.header.date'), key: 'createdAt' },
    { title: t('common.actions'), key: 'actions', sortable: false }
]);

const items = ref<SalesRequestDto[]>([]);

// Handle table actions
const handleAction = ({ action, item }: { action: string; item: any }) => {
    if (action === 'view') {
        // Handle view action
        if (item && item.requestId) {
            router.push({ name: 'pageViewSalesRequest', params: { id: item.requestId } });
        } else {
            // Optionally show an error or warning
            console.error('Invalid or missing sales request ID:', item);
        }
    } 
    else if (action === 'edit') {
        if (item && item.requestId) {
            router.push({ name: 'pageEditSalesRequest', params: { id: item.requestId } });
        } else {
            // Optionally show an error or warning
            console.error('Invalid or missing sales request ID:', item);
        }
    }
    else if (action === 'service_approval') {
        if(item && item.requestId){
            router.push({ name: 'pageServiceApproval', params: { id: item.requestId } });
        } else {
            console.error('Invalid request id for service approval', item);
        }
    }
    else if (action === 'cmac_registration') {
        if (item && item.requestId) {
            router.push({ name: 'pageCmacRegistration', params: { id: item.requestId } });
        } else {
            console.error('Invalid or missing sales request ID for CMAC Registration:', item);
        }
    }
    else if (action === 'cmac_extension') {
        if (item && item.requestId) {
            router.push({ name: 'pageCmacExtension', params: { id: item.requestId } });
        } else {
            console.error('Invalid or missing sales request ID for CMAC Extension:', item);
        }
    }
    else if (action === 'approve') {
        // Handle approve action
        router.push({ name: 'Quote', params: { id: item.id } });
        console.log('Approve request:', item);
    } else if (action === 'reject') {
        // Handle reject action
        router.push({ name: 'pageServiceApproval'});
        console.log('Reject request:', item);
    }
    else if (action === 'pageServiceRequestForm') {
        // Handle reject action
        router.push({ name: 'pageServiceRequestForm', params: { id: item.id } });
        console.log('Reject request:', item);
    } else if (action === 'approve_pricing') {
        // Handle approve pricing action
        router.push({ name: 'Quote', params: { id: item.requestId } });
     
        console.log('Approve pricing for request:', item);
    }
    else if (action === 'requestRevision') {
        // Handle approve pricing action
        openSearchDialog(item)
        console.log('Request Revision for request:', item);
    }
    else if (action === 'assignPDAnalyst') {
        // Handle PD Analyst assignment
        openPDAnalystDialog(item)
        console.log('Assign PD Analyst for request:', item);
    }
};
const openSearchDialog = (item:any) => {
selectedItemId.value = item.requestId;

  showManagerSearchDialog.value = true;
};

const openPDAnalystDialog = (item:any) => {
  selectedItemId.value = item.requestId;
  showPDAnalystSearchDialog.value = true;
};

// Load data
onMounted(async () => {
    appStore.stopPageLoader();
    loading.value = true;
    error.value = '';
    try {
        const data = props.mineOnly ? await getMySalesRequests() : await getSalesRequests();
        items.value = data;
    } catch (e) {
        error.value = t('common.error_loading_data') || 'Failed to load sales requests.';
    } finally {
        loading.value = false;
    }
});

</script>

<template>
    <div class="pa-4">
        <div class="d-flex justify-space-between align-center mb-4">
            <h1>{{ t('page.sales_requests.title') }}</h1>
            <v-btn v-if="actionPermissionChannel( Channel_Action.SALES_OPERATIONS )"
                color="primary" 
                prepend-icon="add" 
                :to="{ name: 'pageNewSalesRequest' }"
            >
                {{ t('page.sales_requests.button.new_request') }}
            </v-btn>
        </div>
        
        <v-card class="mt-4">
 
            
            <!-- <v-card-text> -->
                <DataTable 
                    :headers="headers" 
                    :items="items" 
                    :loading="loading"
                    @action="handleAction"
                >
                <template v-slot:item.dsdRequestStatus="{ item }">
                        <!-- <v-chip
                            :color="item.dsdRequestStatus === DsdRequestStatus.APPROVED ? 'success' : item.dsdRequestStatus === DsdRequestStatus.MANAGER_APPROVAL_PENDING ? 'primary' : 'warning'"
                            size="small"
                        >
                            {{ getDsdRequestStatusText(item.dsdRequestStatus) }}
                        </v-chip> -->
                        <StatusBadge :status="item.dsdRequestStatus || 0" />
                    </template>
                    <template v-slot:item.actions="{ item }">
                        <v-menu>
                            <template v-slot:activator="{ props }">
                                <v-btn
                                    icon="more_vert"
                                    variant="text"
                                    size="small"
                                    v-bind="props"
                                ></v-btn>
                            </template>
                            <v-list>
                                <v-list-item
                                    prepend-icon="visibility"
                                    title="View"
                                    @click="handleAction({ action: 'view', item })"
                                ></v-list-item>
                                <v-list-item
                                    v-if="item.dsdRequestStatus === DsdRequestStatus.DRAFT && actionPermissionChannel(Channel_Action.SALES_OPERATIONS)"
                                    prepend-icon="edit"
                                    title="Edit"
                                    @click="handleAction({ action: 'edit', item })"
                                ></v-list-item>
                        
                                <!-- <v-list-item
                                    prepend-icon="attach_money"
                                    title="Approve Pricing"
                                    @click="handleAction({ action: 'approve_pricing', item })"
                                ></v-list-item> -->
                    

                                   <!-- <v-list-item
                                    
                                    prepend-icon="cancel"
                                    title="Fill Service Form"
                                    @click="handleAction({ action: 'pageServiceRequestForm', item })"
                                ></v-list-item> -->
                                                <!-- <v-list-item
                                    prepend-icon="assignment_turned_in"
                                    title="Service Approval"
                                    @click="handleAction({ action: 'service_approval', item })"
                                ></v-list-item> -->
                                <v-list-item
                                v-if="item.dsdRequestStatus === DsdRequestStatus.APPROVED && (actionPermissionChannel(Channel_Action.PRICE_DESK_ANALYST)|| actionPermissions(CN_Action.ADMIN))"
                                    prepend-icon="app_registration"
                                    title="CMAC Registration"
                                    @click="handleAction({ action: 'cmac_registration', item })"
                                ></v-list-item>
                                <v-list-item
                                v-if="item.dsdRequestStatus === DsdRequestStatus.APPROVE_PRICING_APPROVED && actionPermissionChannel(Channel_Action.PRICE_DESK_OPERATIONS)"
                                    prepend-icon="event"
                                    title="CMAC Extension"
                                    @click="handleAction({ action: 'cmac_extension', item })"
                                ></v-list-item>
                                 <v-list-item  v-if="item.dsdRequestStatus === DsdRequestStatus.APPROVED && actionPermissionChannel(Channel_Action.SALES_OPERATIONS)"
                                    prepend-icon="add"
                                    title="Revision & Addition"
                                    @click="handleAction({ action: '', item })"
                                ></v-list-item> 
                                  <v-list-item  v-if="[DsdRequestStatus.MANAGER_APPROVAL_PENDING, DsdRequestStatus.RL_APPROVAL_PENDING].includes(item.dsdRequestStatus)   && (actionPermissionChannel(Channel_Action.PRICE_DESK_OPERATIONS)|| actionPermissions(CN_Action.ADMIN))"
                                    prepend-icon="change_circle"
                                    title="Change Manager/ RL "
                                    @click="handleAction({ action: 'requestRevision', item })"
                                ></v-list-item>
                                <v-list-item v-if="item.dsdRequestStatus === DsdRequestStatus.PD_APPROVAL_PENDING  && (actionPermissionChannel(Channel_Action.PRICE_DESK_ANALYST)|| actionPermissions(CN_Action.ADMIN))"
                                    prepend-icon="assignment_ind"
                                    title="Assign/Reassign PD Analyst"
                                    @click="handleAction({ action: 'assignPDAnalyst', item })"
                                ></v-list-item> 
            </v-list>
                        </v-menu>
                    </template>
                    <template v-slot:item.createdAt="{ item }">
                        <span>{{ new Date(item.createdAt).toLocaleDateString(undefined, { year: 'numeric', month: '2-digit', day: '2-digit' }) }}</span>
                    </template>
                    <template v-slot:item.customerId="{ item }">
                        <span>{{ !!item.customer ? item.customer.displayName : item.customerId }}</span>
                    </template>
                </DataTable>
            <!-- </v-card-text> -->
        </v-card>
    </div>
    <ManagerChange
v-model="showManagerSearchDialog"
  :item-id="selectedItemId !== null ? selectedItemId : undefined"
/>
    <PDAnalystChange
v-model="showPDAnalystSearchDialog"
  :item-id="selectedItemId !== null ? selectedItemId : undefined"
/>
</template>
