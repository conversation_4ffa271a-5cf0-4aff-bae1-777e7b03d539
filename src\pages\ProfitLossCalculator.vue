<template>
  <v-container fluid class="pnl-page">
    <!-- Header Section -->
    <v-row class="mb-4 header-section align-center">
      <v-col cols="12" md="5">
        <v-text-field
          v-model="customerName"
          label="Customer Name"
          variant="outlined"
          density="compact"
          hide-details
          readonly
        ></v-text-field>
      </v-col>
      <v-col cols="12" md="3">
        <v-text-field
          v-model="category"
          label="Category"
          variant="outlined"
          density="compact"
          hide-details
          readonly
        ></v-text-field>
      </v-col>
      <v-col>
        <StatusBadge :status="isPnlApproved==='Y' ? DsdRequestStatus.APPROVAL_PRICING_PENDING : DsdRequestStatus.PD_APPROVAL_PENDING" />
      </v-col>
      <v-col cols="12" md="" class="text-right">
        <v-menu location="bottom end">
          <template #activator="{ props }">
            <v-btn v-bind="props" variant="text" >
              Actions
              <v-icon>arrow_drop_down</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item v-if="!viewOnly" @click="resetGlobalValues">
              <template #prepend>
                <v-icon color="secondary">refresh</v-icon>
              </template>
              <v-list-item-title>Reset All</v-list-item-title>
            </v-list-item>
            <v-list-item v-if="!viewOnly" @click="rejectRequest">
              <template #prepend>
                <v-icon color="error">error</v-icon>
              </template>
              <v-list-item-title>Reject Changes</v-list-item-title>
            </v-list-item>
            <v-list-item v-if="!viewOnly" @click="approveRequest">
              <template #prepend>
                <v-icon color="success">check</v-icon>
              </template>
              <v-list-item-title>Approve Changes</v-list-item-title>
            </v-list-item>
            <v-list-item  @click="exportToCSV">
              <template #prepend>
                <v-icon color="primary">download</v-icon>
              </template>
              <v-list-item-title>Export to CSV</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </v-col>
    </v-row>

    <!-- Approve/Reject Remarks Dialog -->
    <v-dialog v-model="showRemarkDialog" max-width="520">
      <v-card>
        <v-card-title class="text-h6">
          {{ remarkAction === 'approve' ? 'Approve Changes' : 'Reject Changes' }}
        </v-card-title>
        <v-card-text>
          <v-textarea
            v-model="remarkText"
            label="Remarks"
            variant="outlined"
            density="comfortable"
            auto-grow
            rows="3"
            clearable
          />
        </v-card-text>
        <v-card-actions class="justify-end">
          <v-btn variant="text" @click="onCancelRemark">Cancel</v-btn>
          <v-btn v-if="remarkAction === 'approve'" color="success" @click="onConfirmRemark">Approve</v-btn>
          <v-btn v-else color="error" @click="onConfirmRemark">Reject</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>


    <!-- Category Tabs -->
    <v-tabs v-model="activeTab" grow class="canon-tabs">
      <v-tab value="hardware">Hardware</v-tab>
      <v-tab value="solution">Solution</v-tab>
      <v-tab value="requestDetails">Request Details</v-tab>
    </v-tabs>

    <div class="d-flex justify-end mb-2">
      <v-switch
        v-model="showDetailedColumns"
        label="Show Detailed Columns"
        color="primary"
        hide-details
        density="compact"
      />
    </div>

    <v-window v-model="activeTab" class="pnl-window">
      <!-- Hardware Tab Content -->
      <v-window-item value="hardware">
        <!-- Deal Summary -->
        <v-card class="mb-6 deal-summary-card" outlined>
          <v-card-title class="text-h6">Deal Summary</v-card-title>
          <v-card-text>
            <v-row dense>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Total Quantity</div>
                <div class="value">{{ currentDealSummary.totalQuantity }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>MSRP</div>
                <div class="value">{{ formatCurrency(currentDealSummary.totalMSRP) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Requested Selling Price</div>
                <div class="value highlighted-summary-value">{{ formatCurrency(currentDealSummary.totalRequestedSellingPrice) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Percentage of MSRP</div>
                <div class="value">{{ currentDealSummary.percentageOfMSRP.toFixed(2) }}%</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Cost</div>
                <div class="value">{{ formatCurrency(currentDealSummary.totalProductCost) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Total Cost</div>
                <div class="value highlighted-summary-value">{{ formatCurrency(currentDealSummary.overallTotalCost) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Total CMAC Support Amount</div>
                <div class="value">{{ formatCurrency(currentDealSummary.grandTotalCmacSupportAmount) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Total CMAC Cost</div>
                <div class="value">{{ formatCurrency(currentDealSummary.grandTotalCMACCost) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>GP %</div>
                <div class="value" :class="currentDealSummary.gpPercentage >= 0 ? 'text-green' : 'text-red'">{{ currentDealSummary.gpPercentage.toFixed(2) }}%</div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <!-- Global Controls -->
        <v-card class="mb-4" outlined v-if="!viewOnly">
          <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
              Adjustments
          </v-card-title>
          <v-card-text>
            <v-row dense align="center">
              <v-col cols="12" sm="6" md="3">
                <v-text-field
                  v-model.number="globalDiscount"
                  label="Discount"
                  type="number"
                  variant="outlined"
                  density="compact"
                  :readonly="viewOnly"
                  hide-details
                  step="0.1"
                  min="0"
                  max="100"
                  suffix="%"
                  @update:model-value="debouncedApplyGlobalDiscount"
                />
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-text-field
                  v-model.number="globalTargetGP"
                  label="Target GP %"
                  type="number"
                  variant="outlined"
                  density="compact"
                  :readonly="viewOnly"
                  hide-details
                  step="0.1"
                  min="-100"
                  max="100"
                  suffix="%"
                  @update:model-value="debouncedApplyGlobalTargetGP"
                />
              </v-col>

            </v-row>
          </v-card-text>
        </v-card>



        <!-- Product Groups -->
        <div v-for="(group, groupIndex) in currentGroups" :key="groupIndex" class="mb-6">
          <v-card outlined>
            <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
              {{ group.groupName }}
            </v-card-title>
            <div style="overflow-x: auto; width: 100%;">
              <v-table density="compact">
                <thead>
                  <tr>
                    <th>Product</th>
                    <th>Item Number</th>
                    <th v-if="showDetailedColumns">BSD Qty</th>
                    <th v-if="showDetailedColumns">Dealer Its</th>
                    <th>Total Qty</th>
                    <th>MSRP</th>
                    <th>Req. Selling Price (Unit)</th>
                    <th v-if="showDetailedColumns">Total Req. Selling Price</th>
                    <th>% of MSRP</th>
                    <th>Cost (Unit)</th>
                    <th v-if="showDetailedColumns">Total Cost</th>
                    <th>CMAC Support (Unit)</th>
                    <th class="support-percentage-column">Support %</th>
                    <th v-if="showDetailedColumns">CMAC Cost (Unit)</th>
                    <th>Total CMAC Cost</th>
                    <th style="width: 95px; min-width: 95px;">GP %</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, itemIndex) in group.items" :key="`item-${itemIndex}`">
                    <td style="min-width: 200px;">{{ item.product }}</td>
                    <td>{{ item.itemNumber }}</td>
                    <td v-if="showDetailedColumns">{{ item.bsdQuantity }}</td>
                    <td v-if="showDetailedColumns">{{ item.dealerIts }}</td>
                    <td>{{ (item.bsdQuantity || 0) + (item.dealerIts || 0) }}</td>
                    <td>{{ formatCurrency(item.msrp) }}</td>
                    <td>
                      <v-text-field
                        :class="validationClass(item, 'price')"
                        v-model.number="item.unitRequestedSellingPrice"
                        type="number"
                        variant="underlined"
                        density="compact"
                        :readonly="viewOnly"
                        hide-details
                        step="0.01"
                        min="0"
                        @update:model-value="updateSellingPrice(item)"
                      />
                    </td>
                    <td v-if="showDetailedColumns">{{ formatCurrency(item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                    <td>{{ item.msrp && item.unitRequestedSellingPrice ? ((item.unitRequestedSellingPrice / item.msrp) * 100).toFixed(2) + '%' : 'N/A' }}</td>
                    <td>{{ formatCurrency(item.unitCost) }}</td>
                    <td v-if="showDetailedColumns">{{ formatCurrency(item.unitCost * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                    <td>
                      <v-text-field
                        :class="validationClass(item, 'supportValue')"
                        v-model.number="item.cmacSupportValue"
                        type="number"
                        variant="underlined"
                        density="compact"
                        :readonly="viewOnly"
                        hide-details
                        step="0.01"
                        min="0"
                        @update:model-value="updateFromSupportValue(item)"
                      />
                    </td>
                    <td class="support-percentage-column">
                      <v-text-field
                        :class="validationClass(item, 'supportPercentage')"
                        v-model.number="item.supportPercentage"
                        type="number"
                        variant="underlined"
                        density="compact"
                        :readonly="viewOnly"
                        hide-details
                        step="0.01"
                        min="0"
                        max="100"
                        suffix="%"
                        @update:model-value="updateFromSupportPercentage(item)"
                      />
                    </td>
                    <td v-if="showDetailedColumns">{{ formatCurrency(item.unitCost - item.cmacSupportValue) }}</td>
                    <td>{{ formatCurrency((item.unitCost - item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                    <td :class="{'text-red': (((item.unitRequestedSellingPrice - item.unitCost + item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) / ((item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) || 1) * 100) < 0}">
                      <v-text-field
                        :class="validationClass(item, 'gp')"
                        v-model.number="item.gpPercentage"
                        type="number"
                        variant="underlined"
                        density="compact"
                        :readonly="viewOnly"
                        hide-details
                        step="0.01"
                        min="-100"
                        max="100"
                        suffix="%"
                        @update:model-value="updateFromGPPercentage(item)"
                      />
                    </td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr class="font-weight-bold group-totals-row">
                    <td colspan="2">Group Totals:</td>
                    <td v-if="showDetailedColumns">{{ getGroupTotals(group).totalBsdQuantity }}</td>
                    <td v-if="showDetailedColumns">{{ getGroupTotals(group).totalDealerIts }}</td>
                    <td>{{ getGroupTotals(group).totalQuantity }}</td>
                    <td>{{ formatCurrency(getGroupTotals(group).totalMSRP) }}</td>
                    <td>{{" "}}</td>
                    <td v-if="showDetailedColumns">{{ formatCurrency(getGroupTotals(group).totalRequestedSellingPrice) }}</td>
                    <td>{{ getGroupTotals(group).avgPercentageOfMSRP.toFixed(2) }}%</td>
                    <td>{{" "}}</td>
                    <td v-if="showDetailedColumns">{{ formatCurrency(getGroupTotals(group).totalItemCost) }}</td>
                    <td>{{" "}}</td>
                    <td>{{" "}}</td>
                    <td v-if="showDetailedColumns">{{" "}}</td>
                    <td>{{ formatCurrency(getGroupTotals(group).totalGroupCMACCost) }}</td>
                    <td>{{ getGroupTotals(group).groupGPPercentage.toFixed(2) }}%</td>
                  </tr>
                </tfoot>
              </v-table>
            </div>
          </v-card>
        </div>

        <!-- Final Summary Section -->
        <v-card class="mt-6 final-summary-card" outlined>
          <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
            Deal Cost Summary & GP
          </v-card-title>
          <v-list dense class="pa-0">
            <v-list-item>
              <v-row no-gutters>
                <v-col cols="8">Product Table Total CMAC Support Amount:</v-col>
                <v-col cols="4" class="text-right">{{ formatCurrency(currentDealSummary.grandTotalCmacSupportAmount) }}</v-col>
              </v-row>
            </v-list-item>
            <v-divider class="my-2"></v-divider>
            <v-list-item class="font-weight-bold">
              <v-row no-gutters>
                <v-col cols="8">Subtotal of Above Costs:</v-col>
                <v-col cols="4" class="text-right">{{ formatCurrency(currentDealSummary.subtotalOfAboveCosts) }}</v-col>
              </v-row>
            </v-list-item>
            <v-divider class="my-2"></v-divider>
            <v-list-item class="font-weight-bold success--text">
              <v-row no-gutters>
                <v-col cols="8">Overall GP %:</v-col>
                <v-col cols="4" class="text-right">{{ currentDealSummary.gpPercentage.toFixed(2) }}%</v-col>
              </v-row>
            </v-list-item>
          </v-list>
        </v-card>
      </v-window-item>

      <!-- Solution Tab Content -->
      <v-window-item value="solution">
        <!-- Deal Summary -->
        <v-card class="mb-6 deal-summary-card" outlined>
          <v-card-title class="text-h6">Deal Summary</v-card-title>
          <v-card-text>
            <v-row dense>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Total Quantity</div>
                <div class="value">{{ currentDealSummary.totalQuantity }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>MSRP</div>
                <div class="value">{{ formatCurrency(currentDealSummary.totalMSRP) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Requested Selling Price</div>
                <div class="value highlighted-summary-value">{{ formatCurrency(currentDealSummary.totalRequestedSellingPrice) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Percentage of MSRP</div>
                <div class="value">{{ currentDealSummary.percentageOfMSRP.toFixed(2) }}%</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Cost</div>
                <div class="value">{{ formatCurrency(currentDealSummary.totalProductCost) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Total Cost</div>
                <div class="value highlighted-summary-value">{{ formatCurrency(currentDealSummary.overallTotalCost) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Total CMAC Support Amount</div>
                <div class="value">{{ formatCurrency(currentDealSummary.grandTotalCmacSupportAmount) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>Total CMAC Cost</div>
                <div class="value">{{ formatCurrency(currentDealSummary.grandTotalCMACCost) }}</div>
              </v-col>
              <v-col cols="12" sm="6" md="4" lg="4">
                <div>GP %</div>
                <div class="value" :class="currentDealSummary.gpPercentage >= 0 ? 'text-green' : 'text-red'">{{ currentDealSummary.gpPercentage.toFixed(2) }}%</div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <!-- Global Controls -->
        <v-card class="mb-4" outlined v-if="!viewOnly">
          <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
            Adjustments
          </v-card-title>
          <v-card-text>
            <v-row dense align="center">
              <v-col cols="12" sm="6" md="3">
                <v-text-field
                  v-model.number="globalDiscount"
                  label="Discount"
                  type="number"
                  variant="outlined"
                  density="compact"
                  :readonly="viewOnly"
                  hide-details
                  step="0.1"
                  min="0"
                  max="100"
                  suffix="%"
                  @update:model-value="debouncedApplyGlobalDiscount"
                />
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-text-field
                  v-model.number="globalTargetGP"
                  label="Target GP %"
                  type="number"
                  variant="outlined"
                  density="compact"
                  :readonly="viewOnly"
                  hide-details
                  step="0.1"
                  min="-100"
                  max="100"
                  suffix="%"
                  @update:model-value="debouncedApplyGlobalTargetGP"
                />
              </v-col>
              <!-- <v-col cols="12" sm="6" md="3">
                <v-btn
                  color="secondary"
                  variant="outlined"
                  prepend-icon="refresh"
                  @click="resetGlobalValues"
                  block
                  v-if="!viewOnly"
                >
                  Reset All
                </v-btn>
              </v-col>
              <v-col cols="12" sm="6" md="3">
                <v-btn
                  color="success"
                  variant="outlined"
                  prepend-icon="check"
                  @click="saveChanges"
                  block
                  v-if="!viewOnly"
                >
                  Approve Changes
                </v-btn>
              </v-col> -->
            </v-row>
          </v-card-text>
        </v-card>

        <!-- Product Groups -->
        <div v-for="(group, groupIndex) in solutionGroups" :key="groupIndex" class="mb-6">
          <v-card outlined>
            <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
              {{ group.groupName }}
            </v-card-title>
            <div style="overflow-x: auto; width: 100%;">
              <v-table density="compact">
                <thead>
                  <tr>
                    <th>Product</th>
                    <th>Item Number</th>
                    <th v-if="showDetailedColumns">BSD Qty</th>
                    <th v-if="showDetailedColumns">Dealer Its</th>
                    <th>Total Qty</th>
                    <th>MSRP</th>
                    <th>Req. Selling Price (Unit)</th>
                    <th v-if="showDetailedColumns">Total Req. Selling Price</th>
                    <th>% of MSRP</th>
                    <th>Cost (Unit)</th>
                    <th v-if="showDetailedColumns">Total Cost</th>
                    <th>CMAC Support (Unit)</th>
                    <th class="support-percentage-column">Support %</th>
                    <th v-if="showDetailedColumns">CMAC Cost (Unit)</th>
                    <th>Total CMAC Cost</th>
                    <th style="width: 95px; min-width: 95px;">GP %</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, itemIndex) in group.items" :key="`item-${itemIndex}`">
                    <td style="min-width: 200px;">{{ item.product }}</td>
                    <td>{{ item.itemNumber }}</td>
                    <td v-if="showDetailedColumns">{{ item.bsdQuantity }}</td>
                    <td v-if="showDetailedColumns">{{ item.dealerIts }}</td>
                    <td>{{ (item.bsdQuantity || 0) + (item.dealerIts || 0) }}</td>
                    <td>{{ formatCurrency(item.msrp) }}</td>
                    <td>
                      <v-text-field
                        :class="validationClass(item, 'price')"
                        v-model.number="item.unitRequestedSellingPrice"
                        type="number"
                        variant="underlined"
                        density="compact"
                        :readonly="viewOnly"
                        hide-details
                        step="0.01"
                        min="0"
                        @update:model-value="updateSellingPrice(item)"
                      />
                    </td>
                    <td v-if="showDetailedColumns">{{ formatCurrency(item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                    <td>{{ item.msrp && item.unitRequestedSellingPrice ? ((item.unitRequestedSellingPrice / item.msrp) * 100).toFixed(2) + '%' : 'N/A' }}</td>
                    <td>{{ formatCurrency(item.unitCost) }}</td>
                    <td v-if="showDetailedColumns">{{ formatCurrency(item.unitCost * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                    <td>
                      <v-text-field
                        :class="validationClass(item, 'supportValue')"
                        v-model.number="item.cmacSupportValue"
                        type="number"
                        variant="underlined"
                        density="compact"
                        :readonly="viewOnly"
                        hide-details
                        step="0.01"
                        min="0"
                        @update:model-value="updateFromSupportValue(item)"
                      />
                    </td>
                    <td class="support-percentage-column">
                      <v-text-field
                        :class="validationClass(item, 'supportPercentage')"
                        v-model.number="item.supportPercentage"
                        type="number"
                        variant="underlined"
                        density="compact"
                        :readonly="viewOnly"
                        hide-details
                        step="0.01"
                        min="0"
                        max="100"
                        suffix="%"
                        @update:model-value="updateFromSupportPercentage(item)"
                      />
                    </td>
                    <td v-if="showDetailedColumns">{{ formatCurrency(item.unitCost - item.cmacSupportValue) }}</td>
                    <td>{{ formatCurrency((item.unitCost - item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) }}</td>
                    <td :class="{'text-red': (((item.unitRequestedSellingPrice - item.unitCost + item.cmacSupportValue) * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) / ((item.unitRequestedSellingPrice * ((item.bsdQuantity || 0) + (item.dealerIts || 0))) || 1) * 100) < 0}">
                      <v-text-field
                        :class="validationClass(item, 'gp')"
                        v-model.number="item.gpPercentage"
                        type="number"
                        variant="underlined"
                        density="compact"
                        :readonly="viewOnly"
                        hide-details
                        step="0.01"
                        min="-100"
                        max="100"
                        suffix="%"
                        @update:model-value="updateFromGPPercentage(item)"
                      />
                    </td>
                  </tr>
                </tbody>
                <tfoot>
                  <tr class="font-weight-bold group-totals-row">
                    <td colspan="2">Group Totals:</td>
                    <td v-if="showDetailedColumns">{{ getGroupTotals(group).totalBsdQuantity }}</td>
                    <td v-if="showDetailedColumns">{{ getGroupTotals(group).totalDealerIts }}</td>
                    <td>{{ getGroupTotals(group).totalQuantity }}</td>
                    <td>{{ formatCurrency(getGroupTotals(group).totalMSRP) }}</td>
                    <td>{{" "}}</td>
                    <td v-if="showDetailedColumns">{{ formatCurrency(getGroupTotals(group).totalRequestedSellingPrice) }}</td>
                    <td>{{ getGroupTotals(group).avgPercentageOfMSRP.toFixed(2) }}%</td>
                    <td>{{" "}}</td>
                    <td v-if="showDetailedColumns">{{ formatCurrency(getGroupTotals(group).totalItemCost) }}</td>
                    <td>{{" "}}</td>
                    <td >{{" "}}</td>
                    <td v-if="showDetailedColumns">{{" "}}</td>
                    <td>{{ formatCurrency(getGroupTotals(group).totalGroupCMACCost) }}</td>
                    <td>{{ getGroupTotals(group).groupGPPercentage.toFixed(2) }}%</td>
                  </tr>
                </tfoot>
              </v-table>
            </div>
          </v-card>
        </div>

        <!-- Final Summary Section -->
        <v-card class="mt-6 final-summary-card" outlined>
          <v-card-title class="text-subtitle-1 grey lighten-3 pa-2">
            Deal Cost Summary & GP
          </v-card-title>
          <v-list dense class="pa-0">
            <v-list-item>
              <v-row no-gutters>
                <v-col cols="8">Product Table Total CMAC Support Amount:</v-col>
                <v-col cols="4" class="text-right">{{ formatCurrency(currentDealSummary.grandTotalCmacSupportAmount) }}</v-col>
              </v-row>
            </v-list-item>
            <v-divider class="my-2"></v-divider>
            <v-list-item class="font-weight-bold">
              <v-row no-gutters>
                <v-col cols="8">Subtotal of Above Costs:</v-col>
                <v-col cols="4" class="text-right">{{ formatCurrency(currentDealSummary.subtotalOfAboveCosts) }}</v-col>
              </v-row>
            </v-list-item>
            <v-divider class="my-2"></v-divider>
            <v-list-item class="font-weight-bold success--text">
              <v-row no-gutters>
                <v-col cols="8">Overall GP %:</v-col>
                <v-col cols="4" class="text-right">{{ currentDealSummary.gpPercentage.toFixed(2) }}%</v-col>
              </v-row>
            </v-list-item>
          </v-list>
        </v-card>
      </v-window-item>

      <!-- Request Details Tab Content -->
      <v-window-item value="requestDetails">
        <PreviewTab :request-id="numericRequestId" :isPnlApproved="isPnlApproved" />
      </v-window-item>

    </v-window>

  </v-container>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import type { ProfitLossItem } from '@/services/salesRequestService';
import { getProfitLossData } from '@/services/salesRequestService';
import { ref, computed, watch, onUnmounted, onMounted } from 'vue';
import { saveAs } from 'file-saver';
import { saveProfitLossChanges } from '@/services/salesRequestService';
import { debounce } from 'lodash';
import { useAppStore } from '@/stores/AppStore';
import PreviewTab from '@/components/sales/PreviewTab.vue';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import StatusBadge from '@/components/core/StatusBadge.vue';
import { DsdRequestStatus } from '@/enums/DsdRequestStatus';
import { useI18n } from 'vue-i18n';


interface PNLItem {
  requestItemId?: number;
  itemId?: number;
  product: string;
  itemNumber: string;
  bsdQuantity: number;
  dealerIts: number;
  msrp: number;
  unitRequestedSellingPrice: number;
  unitCost: number;
  cmacSupportValue: number;
  supportPercentage: number;
  gpPercentage: number;
  parentItemId?: number | null;
  // Add any other fields that might be needed for calculations
}

interface ProductGroup {
  groupName: string;
  items: PNLItem[];
}

interface PNLData {
  customerName: string;
  categoryInfo: {
    category: string;
  };
  productGroups: ProductGroup[];
}
const { t } = useI18n();


// --- Split data into Hardware vs Solution groups ---
const customerName = ref('');
const category = ref('');

const hardwareGroups = ref<ProductGroup[]>([]);
const solutionGroups = ref<ProductGroup[]>([]);

// Independent global controls per tab
const hardwareDiscount = ref(0);
const solutionDiscount = ref(0);
const hardwareTargetGP = ref(0);
const solutionTargetGP = ref(0);

// activeTab = 'hardware' | 'solution'
const activeTab = ref<'hardware' | 'solution' | 'requestDetails'>('hardware');

// Convenience computed accessors for the currently visible tab
const currentGroups = computed<ProductGroup[]>(() =>
  activeTab.value === 'hardware' ? hardwareGroups.value : solutionGroups.value
);

const globalDiscount = computed<number>({
  get() {
    return activeTab.value === 'hardware' ? hardwareDiscount.value : solutionDiscount.value;
  },
  set(val: number) {
    if (activeTab.value === 'hardware') hardwareDiscount.value = val; else solutionDiscount.value = val;
  }
});

const globalTargetGP = computed<number>({
  get() {
    return activeTab.value === 'hardware' ? hardwareTargetGP.value : solutionTargetGP.value;
  },
  set(val: number) {
    if (activeTab.value === 'hardware') hardwareTargetGP.value = val; else solutionTargetGP.value = val;
  }
});


const route = useRoute();
const requestId = route.params.id;
// For child components that accept number|string, provide a numeric id when possible
const numericRequestId = computed<number | undefined>(() => {
  const p = route.params.id;
  if (Array.isArray(p)) return Number(p[0]);
  if (typeof p === 'string') return Number(p);
  return undefined;
});
// View-only mode derived from backend P&L approval flag
const isPnlApproved = ref<'Y' | 'N' | undefined>(undefined);
const viewOnly = computed(() => isPnlApproved.value === 'Y');
const showDetailedColumns = ref(false);
const appStore = useAppStore();
const snackbarStore = useSnackbarStore();
const router = useRouter();
// Categorize items into hardware vs solution groups

// Dialog state for Approve/Reject remarks
const showRemarkDialog = ref(false);
const remarkText = ref('');
const remarkAction = ref<'approve' | 'reject'>('approve');

const approveRequest = () => {
  remarkAction.value = 'approve';
  remarkText.value = '';
  showRemarkDialog.value = true;
};

const rejectRequest = () => {
  remarkAction.value = 'reject';
  remarkText.value = '';
  showRemarkDialog.value = true;
};

const onCancelRemark = () => {
  showRemarkDialog.value = false;
};

const onConfirmRemark = async () => {
  showRemarkDialog.value = false;
  await saveChanges(remarkText.value);
};

const categorizeGroups = (items: ProfitLossItem[]): { hardware: ProductGroup[]; solution: ProductGroup[] } => {
  const createMap = () => ({ } as Record<string, ProductGroup>);
  const hardwareMap = createMap();
  const solutionMap = createMap();

  items.forEach(item => {
    const isSolution = item.isSolution === 'Y';
    if(item.msrp == 0){return;}
    const map = isSolution ? solutionMap : hardwareMap;
    const groupKey = item.modelName || item.displayName || 'Others';
    if (!map[groupKey]) {
      map[groupKey] = { groupName: groupKey, items: [] } as ProductGroup;
    }

    const pnlItem: PNLItem = {
      product: item.displayName || item.modelName || '',
      itemNumber: item.itemNumber || '',
      bsdQuantity: item.dsdQuantity || 0,
      dealerIts: item.dealerIt || 0,
      msrp: item.msrp || 0,
      unitRequestedSellingPrice: (item.requestedSellingPricePL ?? item.requestedSellingPrice) || 0,
      unitCost: item.wholesaleCost || 0,
      cmacSupportValue: item.cmacPrice || 0,
      supportPercentage: item.cmacPercentage ?? (item.wholesaleCost ? Math.round(((item.cmacPrice || 0) / item.wholesaleCost) * 10000) / 100 : 0),
      gpPercentage: 0,
      requestItemId: item.requestItemId,
      itemId: item.itemId,
      parentItemId: item.parentItemId
    };

    if (!map[groupKey].items) map[groupKey].items = [];
    map[groupKey].items.push(pnlItem);
  });

  return { hardware: Object.values(hardwareMap), solution: Object.values(solutionMap) };
};



const saveChanges = async (priceDeskRemarkText?: string) => {
  if (viewOnly.value) {
    snackbarStore.show({
      text: 'This Profit & Loss is approved and view-only. Changes cannot be saved.',
      color: 'warning',
      timeout: 3000,
    });
    return;
  }
  const idParam = route.params.id;
  if (!idParam) return;
  const requestIdNum = Number(idParam);
  const targetGroups = activeTab.value === 'hardware' ? hardwareGroups.value : solutionGroups.value;
  const dsdRequestItemInputs = targetGroups.flatMap(g => g.items.map(it => ({
    requestItemId: it.requestItemId ?? 0,
    itemId: it.itemId ?? 0,
    requestId: requestIdNum,
    requestedSellingPricePL: it.unitRequestedSellingPrice,
    cmacPrice: it.cmacSupportValue,
    
  })));
  const payload={dsdRequestItemInputs,action:remarkAction.value,priceDeskRemark: priceDeskRemarkText || ''};
  try {
    appStore.startPageLoader();
    await saveProfitLossChanges(requestIdNum, payload);
    snackbarStore.show({
      text: 'P&L changes approved successfully',
      color: 'success',
      timeout: 3000,
    });
    router.push({ name: 'pagePriceDesk' });
  } catch (e:any) {

       snackbarStore.show({
      text: t(e.response.data.errorMessage),
      color: 'error',
      timeout: 3000,
    });
    console.error('Failed to save P&L changes', e);
  }
  finally{
    appStore.stopPageLoader();

  }
};

onMounted(async () => {
  const idParam = route.params.id;
  if (!idParam) return;
  try {
    const data = await getProfitLossData(Number(idParam));
    appStore.stopPageLoader();
    customerName.value = data.customer?.displayName ?? ''
    category.value = data.pricingCategoryValue ?? ''
    // Set P&L approval flag from API to control view/edit mode
    isPnlApproved.value = data.isPnlApproved;


    const { hardware, solution } = categorizeGroups(data.items);
    hardwareGroups.value = hardware;
    solutionGroups.value = solution;

    // Compute GP % for all items on initial load
    [hardwareGroups.value, solutionGroups.value].forEach(arr =>
      arr.forEach(group => group.items.forEach(it => recalculateItemDerived(it)))
    );

    // If P&L is not approved, apply 0% target GP calculation to all items
    if (isPnlApproved.value !== 'Y') {
      [hardwareGroups.value, solutionGroups.value].forEach(groupArray => {
        groupArray.forEach(group => {
          group.items.forEach(item => {
            if (!item.unitCost) return;
            // Apply 0% target GP: Selling Price = Net Cost (Unit Cost - CMAC Support)
            const netCost = item.unitCost - item.cmacSupportValue;
            item.unitRequestedSellingPrice = round2(Math.min(netCost, item.msrp));
            // Recalculate GP% to reflect the new price
            recalculateItemDerived(item);
          });
        });
      });
    }
    // TODO set customerName and category if available in response
  } catch (error) {
    appStore.stopPageLoader();
    console.error('Failed to fetch P&L data', error);
  }
});

// Helper to round to two decimal places
const round2 = (val: number) => {
  if (typeof val !== 'number' || isNaN(val)) return 0;
  return Math.round((val + Number.EPSILON) * 100) / 100;
};

// Return 'text-red' when a value is invalid per business rules so UI can highlight it
const validationClass = (item: PNLItem, field: string) => {
  switch (field) {
    case 'price':
      return item.unitRequestedSellingPrice < 0 || (item.msrp && item.unitRequestedSellingPrice > item.msrp) ? 'text-red' : '';
    case 'supportValue':
      return item.cmacSupportValue < 0 || item.cmacSupportValue > item.unitCost ? 'text-red' : '';
    case 'supportPercentage':
      return item.supportPercentage < 0 || item.supportPercentage > 100 ? 'text-red' : '';
    case 'gp':
      return item.gpPercentage < 0 ? 'text-red' : '';
    default:
      return '';
  }
};

const formatCurrency = (value: number) => {
  if (typeof value !== 'number' || isNaN(value)) return '$0.00';
  return value.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
};

const getGroupTotals = (group: ProductGroup) => {
  let totalBsdQuantity = 0;
  let totalDealerIts = 0;
  let totalQuantity = 0;
  let totalMSRP = 0;
  let totalRequestedSellingPrice = 0;
  let totalItemCost = 0;
  let totalCmacSupportAmount = 0;
  let totalGroupCMACCost = 0;

  group.items.forEach(item => {
    const itemTotalQuantity = (item.bsdQuantity || 0) + (item.dealerIts || 0);
    totalBsdQuantity += (item.bsdQuantity || 0);
    totalDealerIts += (item.dealerIts || 0);
    totalQuantity += itemTotalQuantity;
    totalMSRP += item.msrp * itemTotalQuantity;
    totalRequestedSellingPrice += item.unitRequestedSellingPrice * itemTotalQuantity;
    totalItemCost += item.unitCost * itemTotalQuantity;
    totalCmacSupportAmount += item.cmacSupportValue * itemTotalQuantity;
    totalGroupCMACCost += (item.unitCost - item.cmacSupportValue) * itemTotalQuantity;
  });

  const groupGrossProfit = totalRequestedSellingPrice - totalItemCost + totalCmacSupportAmount;
  const groupGPPercentage = totalRequestedSellingPrice !== 0 ? (groupGrossProfit / totalRequestedSellingPrice) * 100 : 0;
  const avgPercentageOfMSRP = totalMSRP !== 0 ? (totalRequestedSellingPrice / totalMSRP) * 100 : 0;

  return {
    totalBsdQuantity,
    totalDealerIts,
    totalQuantity,
    totalMSRP,
    totalRequestedSellingPrice,
    avgPercentageOfMSRP,
    totalItemCost: totalItemCost,
    totalCmacSupportAmount,
    totalGroupCMACCost,
    totalGroupGP: groupGrossProfit,
    groupGPPercentage,
  };
};

const updateSellingPrice = (item: PNLItem) => {
  if (item.msrp && item.unitRequestedSellingPrice >= 0) {
    // Store the current GP% to preserve it
    const currentGP = item.gpPercentage;

    // Round the selling price first
    item.unitRequestedSellingPrice = round2(item.unitRequestedSellingPrice);

    // Calculate required CMAC support to maintain the same GP%
    // GP% = (SellingPrice - (UnitCost - CmacSupport)) / SellingPrice * 100
    // Rearranging: CmacSupport = UnitCost - SellingPrice * (1 - GP%/100)
    if (currentGP !== undefined && item.unitRequestedSellingPrice > 0) {
      const requiredCmacSupport = item.unitCost - (item.unitRequestedSellingPrice * (1 - currentGP / 100));
      item.cmacSupportValue = Math.max(0, round2(requiredCmacSupport));
    } else {
      // Fallback: calculate CMAC support as difference between cost and selling price
      item.cmacSupportValue = Math.max(0, round2(item.unitCost - item.unitRequestedSellingPrice));
    }

    // Update support percentage based on new CMAC value
    if (item.unitCost > 0) {
      item.supportPercentage = round2((item.cmacSupportValue / item.unitCost) * 100);
    } else {
      item.supportPercentage = 0;
    }

    // Recalculate GP% with corrected formula
    recalculateItemDerived(item);
  }
  recalculate();
};

const updateFromSupportValue = (item: PNLItem) => {
  if (item.cmacSupportValue >= 0) {
    // Calculate new selling price based on CMAC support
    item.unitRequestedSellingPrice = Math.max(0, item.unitCost - item.cmacSupportValue);

    // round values
    item.unitRequestedSellingPrice = round2(item.unitRequestedSellingPrice);
    item.cmacSupportValue = round2(item.cmacSupportValue);

    // Update support % based on unitCost
    if (item.unitCost) {
      item.supportPercentage = round2((item.cmacSupportValue / item.unitCost) * 100);
    } else {
      item.supportPercentage = 0;
    }
  }
  recalculate();
};

const updateFromGPPercentage = (item: PNLItem) => {
  // Calculate selling price from GP% using correct formula
  // GP% = (SellingPrice - NetCost) / SellingPrice * 100
  // Rearranging: SellingPrice = NetCost / (1 - GP%/100)
  const netCost = item.unitCost - item.cmacSupportValue;
  if (netCost <= 0) return;

  const gpDecimal = item.gpPercentage / 100; // convert to decimal
  if (gpDecimal >= 1) return; // Prevent division by zero or negative

  const newSellingPrice = round2(netCost / (1 - gpDecimal));
  item.unitRequestedSellingPrice = round2(Math.min(newSellingPrice, item.msrp));

  // Recalculate derived fields to ensure consistency
  recalculateItemDerived(item);
};

const recalculateItemDerived = (item: PNLItem) => {
  // Recalculate derived fields using correct GP formula
  // GP% = (Revenue - Cost) / Revenue * 100
  // Where Cost = UnitCost - CmacSupport (net cost after support)
  if (item.unitRequestedSellingPrice > 0) {
    const netCost = item.unitCost - item.cmacSupportValue;
    item.gpPercentage = round2(((item.unitRequestedSellingPrice - netCost) / item.unitRequestedSellingPrice) * 100);
  } else {
    item.gpPercentage = 0;
  }
};

const updateFromSupportPercentage = (item: PNLItem) => {
  if (item.supportPercentage >= 0 && item.supportPercentage <= 100) {
    // Calculate new CMAC support value based on support percentage (of unitCost)
    item.cmacSupportValue = round2(item.unitCost * (item.supportPercentage / 100));

    // Preserve existing GP%, adjust selling price to maintain it using correct formula
    // GP% = (SellingPrice - NetCost) / SellingPrice * 100
    // Rearranging: SellingPrice = NetCost / (1 - GP%/100)
    const gpDecimal = item.gpPercentage / 100;
    const netCost = item.unitCost - item.cmacSupportValue;

    if (gpDecimal < 1 && netCost > 0) {
      const newSellingPrice = round2(netCost / (1 - gpDecimal));
      item.unitRequestedSellingPrice = round2(Math.min(newSellingPrice, item.msrp));
    }

    // Ensure GP% is recalculated for consistency
    recalculateItemDerived(item);
  }
  recalculate();
};

// Global controls state



// Apply global discount to all products
const applyGlobalDiscount = () => {
  // Treat Global Discount as desired support %
  globalDiscount.value = round2(globalDiscount.value);

  const targetGroups = activeTab.value === 'hardware' ? hardwareGroups.value : solutionGroups.value;
  targetGroups.forEach(group => {
    group.items.forEach(item => {
      item.supportPercentage = round2(globalDiscount.value);
      // Recalculate selling price to keep existing gp
      updateFromSupportPercentage(item);
    });
  });
};

// Apply target GP to all products
const applyGlobalTargetGP = () => {
  globalTargetGP.value = round2(globalTargetGP.value);

  const targetGroups = activeTab.value === 'hardware' ? hardwareGroups.value : solutionGroups.value;
  targetGroups.forEach(group => {
    group.items.forEach(item => {
      if (!item.unitCost) return;

      // Calculate selling price based on target GP%
      // For 0% GP: Selling Price = Net Cost (Unit Cost - CMAC Support)
      // For other GP%: Selling Price = Net Cost / (1 - GP%/100)
      const targetGPDecimal = globalTargetGP.value / 100;
      const netCost = item.unitCost - item.cmacSupportValue;

      let newSellingPrice;
      if (globalTargetGP.value === 0) {
        // 0% GP means selling price equals net cost
        newSellingPrice = netCost;
      } else {
        // Use correct GP formula: SellingPrice = NetCost / (1 - GP%/100)
        if (targetGPDecimal >= 1) return; // Prevent division by zero or negative
        newSellingPrice = netCost / (1 - targetGPDecimal);
      }

      // Ensure it does not exceed MSRP
      item.unitRequestedSellingPrice = round2(Math.min(newSellingPrice, item.msrp));
      // Recalculate GP% using the correct formula
      recalculateItemDerived(item);
      // Do NOT touch cmacSupportValue or supportPercentage here
    });
  });
  recalculate();
};

// Debounced update functions
const debouncedApplyGlobalDiscount = debounce(applyGlobalDiscount, 300);
const debouncedApplyGlobalTargetGP = debounce(applyGlobalTargetGP, 300);

// Clean up debounce on component unmount
onUnmounted(() => {
  debouncedApplyGlobalDiscount.cancel();
  debouncedApplyGlobalTargetGP.cancel();
});

// Reset all global values
const resetGlobalValues = () => {
  globalDiscount.value = 0;
  globalTargetGP.value = 0;

  // Reset all products to their original MSRP
  const targetGroups = activeTab.value === 'hardware' ? hardwareGroups.value : solutionGroups.value;
  targetGroups.forEach(group => {
    group.items.forEach(item => {
      if (item.msrp) {
        item.unitRequestedSellingPrice = item.msrp;
        updateSellingPrice(item);
      }
    });
  });
};

const recalculate = () => {
  // Any global recalculation logic can go here
  // Currently handled by computed properties and individual field updates
};

const exportToCSV = () => {
    // Only export hardware data, grouping accessories under their parent machine
    const groups = hardwareGroups.value;
    const allItems: PNLItem[] = groups.flatMap(g => g.items);

    // Filter out items with 0% discount (support percentage) and maintain original order
    const filteredItems = allItems.filter(it => (it.supportPercentage ?? 0) > 0);
    const machines = filteredItems.filter(it => it.parentItemId == null);
    const csvRows: (string | number)[][] = [];

    // CSV Headers based on the required format
    const headers = [
      'machine code',
      'accessories code',
      'Quantity',
      'discount',
      'Dealer GP'
    ];
    csvRows.push(headers);

    // Track accessories that get exported alongside a machine
    const addedAccessories = new Set<PNLItem>();

    machines.forEach(machine => {
      const machineCode = machine.itemNumber || '';
      const machineQty = (machine.bsdQuantity || 0) + (machine.dealerIts || 0);

      // Find accessories for this machine (preserve order from filteredItems)
      const accessoriesForMachine = filteredItems.filter(it => it.parentItemId != null && it.parentItemId === machine.itemId);

      if (accessoriesForMachine.length > 0) {
        // Export the machine row first (only if machine has >0% discount)
        const mDiscountPercent = machine.supportPercentage ?? 0;
        const mDealerGP = machine.gpPercentage ?? 0;
        if (mDiscountPercent > 0) {
          csvRows.push([
            machineCode,                         // machine code
            machineCode,                         // accessories code (same as machine for machine row)
            machineQty,
            `${mDiscountPercent.toFixed(2)}%`,
            `${mDealerGP.toFixed(2)}%`
          ]);
        }

        accessoriesForMachine.forEach(acc => {
          addedAccessories.add(acc);
          const totalQty = (acc.bsdQuantity || 0) + (acc.dealerIts || 0);
          const discountPercent = acc.supportPercentage ?? 0;
          const dealerGP = acc.gpPercentage ?? 0;
          // Only export accessories with >0% discount
          if (discountPercent > 0) {
            csvRows.push([
              machineCode,                         // machine code
              acc.itemNumber || '',                // accessories code
              totalQty,                            // Quantity
              `${discountPercent.toFixed(2)}%`,    // discount (Support %)
              `${dealerGP.toFixed(2)}%`            // Dealer GP
            ]);
          }
        });
      } else {
        // Machine without accessories: export only if >0% discount
        const discountPercent = machine.supportPercentage ?? 0;
        const dealerGP = machine.gpPercentage ?? 0;
        if (discountPercent > 0) {
          csvRows.push([
            machineCode,                           // machine code
            machineCode,                           // accessories code (same as machine)
            machineQty,                            // Quantity
            `${discountPercent.toFixed(2)}%`,      // discount (Support %)
            `${dealerGP.toFixed(2)}%`              // Dealer GP
          ]);
        }
      }
    });

    // Handle orphan accessories (parent not present as a machine in the list)
    const machineIds = new Set<number | undefined>(machines.map(m => m.itemId));
    filteredItems.forEach(item => {
      if (item.parentItemId != null && !addedAccessories.has(item)) {
        if (!machineIds.has(item.parentItemId)) {
          const qty = (item.bsdQuantity || 0) + (item.dealerIts || 0);
          const discountPercent = item.supportPercentage ?? 0;
          const dealerGP = item.gpPercentage ?? 0;
          const code = item.itemNumber || '';
          // Only export if >0% discount (already filtered but double-check)
          if (discountPercent > 0) {
            csvRows.push([
              code,                                 // machine code (no parent found)
              code,                                 // accessories code (same as above)
              qty,
              `${discountPercent.toFixed(2)}%`,
              `${dealerGP.toFixed(2)}%`
            ]);
          }
        }
      }
    });

    // Convert to CSV string
    const csvString = csvRows
      .map(row =>
        row
          .map(cell => {
            const strCell = String(cell ?? '');
            // Escape quotes and wrap in quotes if it contains a comma, newline or quote
            if (strCell.includes(',') || strCell.includes('\n') || strCell.includes('"')) {
              return `"${strCell.replace(/"/g, '""')}"`;
            }
            return strCell;
          })
          .join(',')
      )
      .join('\n');

    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const fileName = `Hardware_Export_${customerName.value}_${new Date().toISOString().split('T')[0]}.csv`;
    saveAs(blob, fileName);
  };

// Reusable function to compute deal summary for any group list
const computeDealSummary = (groups: ProductGroup[]) => {
  let grandTotalBsdQuantity = 0;
  let grandTotalDealerIts = 0;
  let grandTotalQuantity = 0;
  let grandTotalMSRP = 0;
  let grandTotalRequestedSellingPrice = 0;
  let grandTotalProductCost = 0;
  let grandTotalCmacSupportAmount = 0;
  let grandTotalCMACCost = 0;

  groups.forEach(group => {
    const gTotals = getGroupTotals(group);
    grandTotalBsdQuantity += gTotals.totalBsdQuantity;
    grandTotalDealerIts += gTotals.totalDealerIts;
    grandTotalQuantity += gTotals.totalQuantity;
    grandTotalMSRP += gTotals.totalMSRP;
    grandTotalRequestedSellingPrice += gTotals.totalRequestedSellingPrice;
    grandTotalProductCost += gTotals.totalItemCost;
    grandTotalCmacSupportAmount += gTotals.totalCmacSupportAmount;
    grandTotalCMACCost += gTotals.totalGroupCMACCost;
  });

  const overallTotalCost = grandTotalProductCost - grandTotalCmacSupportAmount;
  const gpAmount = grandTotalRequestedSellingPrice - overallTotalCost;
  const gpPercentage = grandTotalRequestedSellingPrice !== 0 ? (gpAmount / grandTotalRequestedSellingPrice) * 100 : 0;
  const percentageOfMSRP = grandTotalMSRP !== 0 ? (grandTotalRequestedSellingPrice / grandTotalMSRP) * 100 : 0;

  return {
    totalBsdQuantity: grandTotalBsdQuantity,
    totalDealerIts: grandTotalDealerIts,
    totalQuantity: grandTotalQuantity,
    totalMSRP: grandTotalMSRP,
    totalRequestedSellingPrice: grandTotalRequestedSellingPrice,
    percentageOfMSRP,
    totalProductCost: grandTotalProductCost,
    grandTotalCMACCost,
    grandTotalCmacSupportAmount,
    overallTotalCost,
    gpPercentage,
    subtotalOfAboveCosts: grandTotalCmacSupportAmount,
  };
};

const currentDealSummaryHardware = computed(() => computeDealSummary(hardwareGroups.value));
const currentDealSummarySolution = computed(() => computeDealSummary(solutionGroups.value));
const currentDealSummary = computed(() =>
  activeTab.value === 'hardware' ? currentDealSummaryHardware.value : currentDealSummarySolution.value
);

// ... (rest of the script remains the same)
</script>

<style scoped>
.pnl-page {
  font-family: Arial, sans-serif;
  font-size: 0.9rem;
}

.header-section .v-input {
  margin-bottom: 0;
}

.deal-summary-card .v-col > div:first-child {
  font-weight: bold;
  color: #555;
  font-size: 0.8rem;
  text-transform: uppercase;
}

.deal-summary-card .value {
  font-size: 1rem;
  font-weight: 500;
}

.deal-summary-card .value {
  font-weight: bold;
  font-size: 1.1em;
}

.deal-summary-card .highlighted-summary-value {
  background-color: #FFF9C4;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #FBC02D;
  display: inline-block;
}

.v-table {
  font-size: 0.85rem;
}

.text-red {
  color: red;
}

.text-green {
  color: green;
}

.group-totals-row td {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center; /* Ensure group totals are centered */
}

.final-summary-card .v-list-item {
  min-height: 36px;
}

.support-percentage-column {
  min-width: 90px; /* Adjust as needed */
}

.v-table thead th {
  white-space: nowrap;
}

.final-summary-card .v-divider {
  border-color: rgba(0, 0, 0, 0.12);
}

.id-commission-card,
.product-group-card {
  border: 1px solid #e0e0e0;
}

.additional-cost-row td,
.id-commission-row td {
  font-style: italic;
  background-color: #f9f9f9;
}

.v-table thead th,
.v-table tbody td {
  text-align: center;
}

.italic-description input,
.italic-description .v-label {
  font-style: italic !important;
}

.highlighted-summary-value {
  background-color: #FFF9C4;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #FBC02D;
  display: inline-block;
}

.additional-cost-row .italic-description,
.id-commission-row .italic-description {
  padding-left: 8px;
}

.additional-cost-row td:nth-child(2),
.id-commission-row td:nth-child(2) {
  font-size: 0.8em;
  color: #555;
  text-align: center;
}
/* Hide spinners from number input fields */
:deep(input[type=number])::-webkit-outer-spin-button,
:deep(input[type=number])::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

:deep(input[type=number]) {
  -moz-appearance: textfield; /* Firefox */
  appearance: textfield;
}
</style>
