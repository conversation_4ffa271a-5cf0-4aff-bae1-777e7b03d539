<template>
  <v-card variant="tonal" class="mt-4">
    <v-card-title class="text-subtitle-2 font-weight-medium">{{ title }}</v-card-title>
    <v-card-text>
      <v-textarea
        v-model="textareaValue"
        variant="outlined"
        density="compact"
        rows="6"
        placeholder="Enter each term on a new line..."
        class="text-caption"
        @input="handleInput"
      />
      <v-chip size="x-small" color="info" variant="text" class="mt-1">
        <v-icon size="x-small" class="mr-1">mdi-information</v-icon>
        Each line will be treated as a separate term
      </v-chip>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  initialTerms: {
    type: Array as () => string[],
    default: () => []
  },
  modelValue: {
    type: Array as () => string[],
    default: () => []
  }
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string[]]
}>();

// Local state
const textareaValue = ref('');

// Initialize textarea with initial terms
onMounted(() => {
  if (props.initialTerms.length > 0) {
    textareaValue.value = props.initialTerms
      .map(term => typeof term === 'object' ? term.text || term : term)
      .join('\n');
  } else if (props.modelValue.length > 0) {
    textareaValue.value = props.modelValue.join('\n');
  }
});

// Watch for changes in initialTerms prop
watch(() => props.initialTerms, (newTerms) => {
  if (newTerms && newTerms.length > 0) {
    textareaValue.value = newTerms
      .map(term => typeof term === 'object' ? term.text || term : term)
      .join('\n');
  }
}, { deep: true });

// Handle input changes
const handleInput = () => {
  const terms = textareaValue.value
    .split('\n')
    .map(term => term.trim())
    .filter(term => term.length > 0);
  
  emit('update:modelValue', terms);
};

// Watch textarea value changes
watch(textareaValue, handleInput);
</script>
