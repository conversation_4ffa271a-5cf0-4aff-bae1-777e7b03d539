<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t, locale } = useI18n();

// Define props - receive data from parent component
const props = defineProps({
  serviceRequestData: {
    type: Object,
    default: () => ({
      serviceFormId: null,
      requestId: null,
      servicePackId: null,
      tonerType: null,
      tonerTypeValue: null,
      servicePackDescription: null,
      region: null,
      territory: null,
      coterm: null,
      isDealerAcceptedSr: null,
      paymentMode: null,
      paymentModeValue: null,
      leaseTermInMonth: null,
      msrp: null,
      msrpPercent: null,
      currentUsageInformation: null,
      currentEquipmentInfo: null,
      serviceBusinessCase: null,
      serviceApprovals: [],
      dsdRequestMfpPricing: []
    })
  }
});

// Define types for the service request data
interface ServiceApproval {
  serviceApprovalId: number;
  serviceFormId: number;
  fixedTermInMonth: number;
  itemId: number;
  displayName: string;
  dsdQuantity: number;
  dealerQuantity: number;
  amvUnit: number;
  colourPercentage: number;
  oversizePercentage: number;
  cpcIncludesAccessory: string;
  blackAndWhite: number;
  colour: number;
  iprc: number;
  minimumBaseAmount: number;
  minimumBaseVolume: number;
  userRemark: string;
}

// Format currency values
const formatCurrency = (value: number | null | undefined) => {
  if (!value && value !== 0) return t('previewTab.na');
  return new Intl.NumberFormat(locale.value || 'en-US', { style: 'currency', currency: 'USD' }).format(value);
};

// Format percentage values
const formatPercentage = (value: number | null | undefined): string => {
  if (!value && value !== 0) return t('previewTab.na');
  return `${value}%`;
};

// Format general values
const formatValue = (value: any): string => {
  if (value === null || value === undefined || value === '') return t('previewTab.na');
  return String(value);
};

// Format rate values (B&W, Colour, IPRC) to 5 decimals
const formatRate5 = (value: number | string | null | undefined): string => {
  if (value === null || value === undefined || value === '') return 'N/A';
  const num = typeof value === 'string' ? parseFloat(value) : value;
  if (typeof num !== 'number' || Number.isNaN(num)) return 'N/A';
  return num.toFixed(5);
};

// Format plain numbers with thousands separators
const formatNumber = (value: number | string | null | undefined): string => {
  if (value === null || value === undefined || value === '') return t('previewTab.na');
  const num = typeof value === 'string' ? Number(String(value).replace(/,/g, '')) : value;
  if (typeof num !== 'number' || Number.isNaN(num)) return t('previewTab.na');
  return new Intl.NumberFormat(locale.value || 'en-US').format(num);
};

// Computed properties for service approvals
const serviceApprovals = computed(() => props.serviceRequestData.serviceApprovals || []);
</script>

<template>
  <div>
    <!-- General Information -->
    <div class="mb-6" v-if="serviceRequestData.territory || serviceRequestData.region || serviceRequestData.tonerType">
      <h3 class="text-subtitle-1 mb-3 text-primary">{{ t('previewTab.serviceRequest.labels.generalInformation') }}</h3>
      <v-table density="compact" class="mb-4">
        <tbody>
          <tr v-if="serviceRequestData.territory">
            <th class="text-left" width="200">{{ t('previewTab.serviceRequest.labels.territory') }}</th>
            <td class="text-left">{{ formatValue(serviceRequestData.territory) }}</td>
          </tr>
          <tr v-if="serviceRequestData.region">
            <th class="text-left">{{ t('previewTab.serviceRequest.labels.region') }}</th>
            <td class="text-left">{{ formatValue(serviceRequestData.region) }}</td>
          </tr>
          <tr v-if="serviceRequestData.tonerType">
            <th class="text-left">{{ t('previewTab.serviceRequest.labels.tonerType') }}</th>
            <td class="text-left">{{ formatValue(serviceRequestData.tonerType) }}</td>
          </tr>
        </tbody>
      </v-table>
    </div>

    <!-- Payment and Lease Details -->
    <div class="mb-6">
      <h3 class="text-subtitle-1 mb-3 text-primary">{{ t('previewTab.serviceRequest.labels.paymentAndLeaseDetails') }}</h3>
      <v-table density="compact" class="mb-4">
        <tbody>
          <!-- <tr>
            <th class="text-left" width="200">Payment Mode</th>
            <td class="text-left">{{ formatValue(serviceRequestData.paymentMode) }}</td>
          </tr> -->
          <tr>
            <th class="text-left">{{ t('previewTab.serviceRequest.labels.paymentModeValue') }}</th>
            <td class="text-left">{{ formatValue(serviceRequestData.paymentModeValue) }}</td>
          </tr>
          <tr>
            <th class="text-left">{{ t('previewTab.serviceRequest.labels.leaseTermMonths') }}</th>
            <td class="text-left">{{ formatValue(serviceRequestData.leaseTermInMonth) }}</td>
          </tr>
          <tr>
            <th class="text-left">{{ t('previewTab.serviceRequest.labels.msrp') }}</th>
            <td class="text-left">{{ formatCurrency(serviceRequestData.msrp) }}</td>
          </tr>
          <tr>
            <th class="text-left">{{ t('previewTab.serviceRequest.labels.msrpPercent') }}</th>
            <td class="text-left">{{ formatPercentage(serviceRequestData.msrpPercent) }}</td>
          </tr>
        </tbody>
      </v-table>
    </div>

    <!-- Service Approvals Table -->
    <div class="mb-6">
      <h3 class="text-subtitle-1 mb-3 text-primary">{{ t('previewTab.sections.cpcRequests') }}</h3>
      <div v-if="serviceApprovals.length > 0">
        <v-table density="compact" class="mb-4">
          <thead>
            <tr>
              <th class="text-left">{{ t('previewTab.cpc.headers.fixedTerm') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.displayName') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.dsdQuantity') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.amvUnit') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.dealerQuantity') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.colourPercent') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.oversizePercent') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.cpcIncludes') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.bw') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.colour') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.iprc') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.minBaseAmount') }}</th>
              <th class="text-left">{{ t('previewTab.cpc.headers.minBaseVolume') }}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(approval, index) in serviceApprovals" :key="index">
              <td>{{ formatValue(approval.fixedTermInMonth) }}</td>
              <td>{{ formatValue(approval.displayName) }}</td>
              <td>{{ formatValue(approval.dsdQuantity) }}</td>
              <td>{{ formatNumber(approval.amvUnit) }}</td>
              <td>{{ formatValue(approval.dealerQuantity) }}</td>
              <td>{{ formatPercentage(approval.colourPercentage) }}</td>
              <td>{{ formatPercentage(approval.oversizePercentage) }}</td>
              <td>{{ formatValue(approval.cpcIncludesAccessory) }}</td>
              <td>{{ formatRate5(approval.blackAndWhite) }}</td>
              <td>{{ formatRate5(approval.colour) }}</td>
              <td>{{ formatRate5(approval.iprc) }}</td>
              <td>{{ formatCurrency(approval.minimumBaseAmount) }}</td>
              <td>{{ formatNumber(approval.minimumBaseVolume) }}</td>
            </tr>
          </tbody>
        </v-table>
      </div>
      <v-alert v-else type="info" class="mb-4">
        {{ t('previewTab.messages.noServiceApprovals') }}
      </v-alert>
    </div>

    <!-- Current Usage Information -->
    <div class="mb-6">
      <h3 class="text-subtitle-1 mb-3 text-primary">Current Usage Information</h3>
      <v-card variant="outlined" class="pa-3">
        <div v-if="serviceRequestData.currentUsageInformation">
          {{ serviceRequestData.currentUsageInformation }}
        </div>
        <v-alert v-else type="info" density="compact">
          No current usage information provided
        </v-alert>
      </v-card>
    </div>

    <!-- Current Equipment Information -->
    <div class="mb-6">
      <h3 class="text-subtitle-1 mb-3 text-primary">Current Equipment Information</h3>
      <v-card variant="outlined" class="pa-3">
        <div v-if="serviceRequestData.currentEquipmentInfo">
          {{ serviceRequestData.currentEquipmentInfo }}
        </div>
        <v-alert v-else type="info" density="compact">
          No current equipment information provided
        </v-alert>
      </v-card>
    </div>

    <!-- Service Business Case -->
    <div class="mb-6">
      <h3 class="text-subtitle-1 mb-3 text-primary">Service Business Case</h3>
      <v-card variant="outlined" class="pa-3">
        <div v-if="serviceRequestData.serviceBusinessCase">
          {{ serviceRequestData.serviceBusinessCase }}
        </div>
        <v-alert v-else type="info" density="compact">
          No service business case provided
        </v-alert>
      </v-card>
    </div>

  </div>
</template>

<style scoped>
.v-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.text-primary {
  color: rgb(var(--v-theme-primary)) !important;
}
</style>
