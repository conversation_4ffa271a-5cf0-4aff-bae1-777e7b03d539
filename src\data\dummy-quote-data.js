// dummy-quote-data.js
// This will be our mock API response / initial data
// dummy-quote-data.js
// Updated to support all sections in QuotePage.vue based on Excel sheet
export const quoteData = {
  documentHeader: {
    companyName: "Canon",
    customerName: "ENTERA UTILITY CONTRACTORS CO LIMITED",
    competitiveKO: {
      status: "NO",
      details: "(see T&Cs below)"
    },
    applicablePromos: null,
    priceValidity: "2025-06-30",
    pricingIssuedOn: "2025-05-25",
    cmacAccountNumber: "CK0856", // As per Excel screenshot
    applicablePromos: "Q2 Special Offer" // Example, fill from Excel if available
  },
  summaryTotals: {
    totalMSRP: 242564.00,
    totalApprovedPricing: 67358.00
  },
  keyInstructions: [
    {
      id: 1,
      type: "CopyBlock",
      text: "3 months Copy Block of 6608 (total) color copies is approved for 3 units of iRC5850. This equal to $350.02 per unit. Sales need to add a total of $1,050.08 to approved pricing and transfer to service."
    }
  ],
  productGroups: [
    {
      id: 'c5850i',
      groupName: "imageRUNNER ADVANCE DX C5850i Configuration",
      mainItem: {
        productDescription: "imageRUNNER ADVANCE DX C5850i",
        itemNumber: "3826C002AA",
        dsdQty: 3,
        dealerTerritoryQty: 0, // Example, ensure this exists
        totalQty: 3,
        msrp: 29797.00,
        approvedSellingPrice: 6777.00,
        approvedSellingPricePercentMSRP: 23
      },
      accessories: [
        { productDescription: "MAX ImagePro 15A", itemNumber: "4280V345", dsdQty: 3, dealerTerritoryQty: 0, totalQty: 3, msrp: 248.00, approvedSellingPrice: 165.00, approvedSellingPricePercentMSRP: 67 },
        // ... more accessories for c5850i
      ],
      groupTotal: {
        msrp: 45116.00, // This might need to be calculated sum of mainItem.msrp * qty + sum of acc.msrp * qty
        approvedSellingPrice: 11380.50, // Similar calculation for approved price
        approvedSellingPricePercentMSRP: 25 // This should be (groupTotal.approvedSellingPrice / groupTotal.msrp) * 100
      }
    },
    {
      id: 'c5860i',
      groupName: "imageRUNNER ADVANCE DX C5860i Configuration",
      mainItem: { productDescription: "imageRUNNER ADVANCE DX C5860i", itemNumber: "3825C002AA", dsdQty: 1, dealerTerritoryQty: 0, totalQty: 1, msrp: 33228.00, approvedSellingPrice: 7556.50, approvedSellingPricePercentMSRP: 23 },
      accessories: [
        { productDescription: "MAX ImagePro 15A", itemNumber: "4280V345", dsdQty: 1, dealerTerritoryQty: 0, totalQty: 1, msrp: 248.00, approvedSellingPrice: 165.00, approvedSellingPricePercentMSRP: 67 },
        { productDescription: "Cassette Feeding Unit-AQ1", itemNumber: "4030C002BA", dsdQty: 1, dealerTerritoryQty: 0, totalQty: 1, msrp: 1871.00, approvedSellingPrice: 550.50, approvedSellingPricePercentMSRP: 29 },
        { productDescription: "Utility Tray-B1", itemNumber: "0165C001AA", dsdQty: 1, dealerTerritoryQty: 0, totalQty: 1, msrp: 96.00, approvedSellingPrice: 57.00, approvedSellingPricePercentMSRP: 59 },
        { productDescription: "Attachment Kit for Reader-A2", itemNumber: "4067C002AA", dsdQty: 1, dealerTerritoryQty: 0, totalQty: 1, msrp: 39.00, approvedSellingPrice: 19.00, approvedSellingPricePercentMSRP: 49 }
      ],
      groupTotal: { msrp: 35482.00, approvedSellingPrice: 8348.00, approvedSellingPricePercentMSRP: 24 }
    },
    {
      id: 'c5870i',
      groupName: "imageRUNNER ADVANCE DX C5870i Configuration",
      mainItem: { productDescription: "imageRUNNER ADVANCE DX C5870i", itemNumber: "3824C002AA", dsdQty: 1, dealerTerritoryQty: 0, totalQty: 1, msrp: 40861.00, approvedSellingPrice: 9287.00, approvedSellingPricePercentMSRP: 23 },
      accessories: [
        { productDescription: "MAX ImagePro 15A", itemNumber: "4280V345", dsdQty: 1, dealerTerritoryQty: 0, totalQty: 1, msrp: 248.00, approvedSellingPrice: 165.00, approvedSellingPricePercentMSRP: 67 },
        { productDescription: "Cassette Feeding Unit-AQ1", itemNumber: "4030C002BA", dsdQty: 1, dealerTerritoryQty: 0, totalQty: 1, msrp: 1871.00, approvedSellingPrice: 550.50, approvedSellingPricePercentMSRP: 29 },
        { productDescription: "Inner Tray (1st Copy Tray Kit-A1)", itemNumber: "4032C001AA", dsdQty: 1, dealerTerritoryQty: 0, totalQty: 1, msrp: 112.00, approvedSellingPrice: 56.00, approvedSellingPricePercentMSRP: 50 },
        { productDescription: "Inner 2way Tray-M1", itemNumber: "4034C001AA", dsdQty: 1, dealerTerritoryQty: 0, totalQty: 1, msrp: 146.00, approvedSellingPrice: 74.00, approvedSellingPricePercentMSRP: 51 }
      ],
      groupTotal: { msrp: 43238.00, approvedSellingPrice: 10132.50, approvedSellingPricePercentMSRP: 23 }
    }
  ],
  softwareLicensing: [
    {
      id: 'uniflow-device',
      category: "uniFLOW (Device Based Licensing)",
      items: [
        { productDescription: "uniFLOW for SMB - MEAP License", itemNumber: "3575B345AA", dsdQty: 4, dealerTerritoryQty: 0, totalQty: 4, msrp: 1177.00, approvedSellingPrice: 743.94, approvedSellingPricePercentMSRP: 63 },
        // ... more items for uniflow-device
      ],
      categoryTotal: {
        msrp: 1642.39, // This should be sum of item.msrp * qty
        approvedSellingPrice: 978.08, // Sum of item.approvedSellingPrice * qty
        approvedSellingPricePercentMSRP: 60 // (categoryTotal.approvedSellingPrice / categoryTotal.msrp) * 100
      }
    },
    {
      id: 'uniflow-one',
      category: "uniFLOW ONE",
      items: [
        { productDescription: "Cloud Print & Scan, Type 1, 1-9 devices", itemNumber: "3575B792AA", dsdQty: 264, dealerTerritoryQty: 0, totalQty: 264, msrp: 60.46, approvedSellingPrice: 29.62, approvedSellingPricePercentMSRP: 49 },
        { productDescription: "MiCard PLUS C", itemNumber: "3575B482AA", dsdQty: 4, dealerTerritoryQty: 0, totalQty: 4, msrp: 400.39, approvedSellingPrice: 203.74, approvedSellingPricePercentMSRP: 51 }
      ],
      categoryTotal: { msrp: 460.85, approvedSellingPrice: 233.35, approvedSellingPricePercentMSRP: 51 }
    }
  ],
  hardwarePriceProgramTerms: {
    chargebacks: "Chargebacks will apply if minimum commitment is not met.",
    equipmentEligibility: "All new Canon imageRUNNER, imagePRESS, and imagePROGRAF hardware.",
    equipmentExclusions: "Used or refurbished equipment, third-party hardware, and specific promotional items unless otherwise stated."
  },
  dsdTerritoryServiceRates: {
    keyInstruction: "3 months Copy Block of 6608 (total) color copies is approved for 3 units of iRC5850. This is equivalent to $350.02 per unit. Sales need to add a total of $1,050.08 to approved pricing and transfer to service.",
    serviceValuePack: "Gold",
    fixedServiceRateTerm: { duration: 60, unit: "months" }, // As per 'Colour Model Terms & Conditions' in Excel
    extraLongLifeNote: "Extra Long Life (B/W and C/MMD Units): Extra long life rates are calculated by multiplying the 'Colour Overage (Letter & Oversize)' rate by 5 (B/W), by 4 (A4), by 3 (A3), by 6 (MICR) and must be specified in contract comments whether LTR sheet feeder is dealer or not.", // Purple box text
    colourModels: [
      { modelName: "IRC5850i", machineQuantity: 3, standardBWCpcRate: 0.00880, standardColourCpcRate: 0.05520, extraLongBwRate: null, extraLongColourRate: null, minimumsApplicable: "NO", accessoryFeesIncludedInCPC: "No", baseBwVolume: "-", baseColourVolume: "-"  },
      { modelName: "IRC5860i", machineQuantity: 1, standardBWCpcRate: 0.00880, standardColourCpcRate: 0.05520, extraLongBwRate: null, extraLongColourRate: null, minimumsApplicable: "NO", accessoryFeesIncludedInCPC: "No", baseBwVolume: "-", baseColourVolume: "-" },
      { modelName: "IRC5870i", machineQuantity: 1, standardBWCpcRate: 0.00880, standardColourCpcRate: 0.05520, extraLongBwRate: null, extraLongColourRate: null, minimumsApplicable: "NO", accessoryFeesIncludedInCPC: "No", baseBwVolume: "-", baseColourVolume: "-" }
      // Add other models from Excel if present, with all new fields
    ]
  },
  colourModelTermsAndConditions: [
    "Service rates are fixed for 60 months (toner-in), with standard annual escalators applying thereafter.",
    "Accessory fees are ONLY included for models where it is NOTED in the service rate table above.",
    "If accessory fees are NOT included, they must be charged to the customer as per service pricebook.",
    "MINIMUMS APPLY AS NOTED IN THE RATE TABLE ABOVE FOR EACH MODEL"
  ],
  generalServiceRelatedTermsAndConditions: [
    "All Service rates quoted above are valid only for DSD territory installation ONLY.",
    "For Dealer territory rates, please refer to the Synergy agreement.",
    "For any discounts off the Synergy price list you need to obtain written dealer acceptance.",
    "Price Desk approvals supersede all other approvals and cannot be combined with any other service exceptions.",
    "All the other terms and conditions in the DSD Service price book are applicable."
  ],
  leaseRateFactors: {
    terms: [
      { months: 36 }, 
      { months: 48 }, 
      { months: 60 }, 
      { months: 66 }
    ],
    rates: [
      {
        type: "LRF - quarterly",
        values: [
          { term: 36, rate: "96.32" },
          { term: 48, rate: "76.01" },
          { term: 60, rate: "63.31" },
          { term: 66, rate: "58.55" }
        ]
      },
      {
        type: "LRF - monthly",
        values: [
          { term: 36, rate: "32.44" },
          { term: 48, rate: "25.81" },
          { term: 60, rate: "21.54" },
          { term: 66, rate: "20.00" }
        ]
      }
    ]
  },
  softCosts: {
    applicabilityNote: "These soft costs are applicable for DSD territory install only. For agent territory, please contact your agent for their specific soft costs. Soft costs are not discountable.",
    deliveryFeesNote: "**Delivery Fees are based on the location specified on the first page of this document. Additional charges may apply for remote locations or special delivery requirements.",
    costItems: [
      { 
        id: "delivery",
        description: "Delivery (Note 1)", 
        costs: {
          'mfp-a3': 200.00,
          'mfp-bw-a4': 160.00,
          'scanner': 80.00,
          'printer': 120.00,
          'production': 450.00
        },
        noteRef: "1" 
      },
      { 
        id: "pickup",
        description: "Pick up (Note 1&2)", 
        costs: {
          'mfp-a3': 150.00,
          'mfp-bw-a4': 125.00,
          'scanner': 60.00,
          'printer': 100.00,
          'production': 350.00
        },
        noteRef: "1&2" 
      },
      { 
        id: "training",
        description: "Training (Note 3)", 
        costs: {
          'mfp-a3': 400.00,
          'mfp-bw-a4': 300.00,
          'scanner': 200.00,
          'printer': 250.00,
          'production': 800.00
        },
        noteRef: "3" 
      },
      { 
        id: "equipment-installation",
        description: "Equipment Installation (Note 4)", 
        costs: {
          'mfp-a3': 500.00,
          'mfp-bw-a4': 450.00,
          'scanner': 300.00,
          'printer': 400.00,
          'production': 1200.00
        },
        noteRef: "4" 
      },
      { 
        id: "software-installation",
        description: "Software Installation", 
        costs: {
          'mfp-a3': 350.00,
          'mfp-bw-a4': 300.00,
          'scanner': 250.00,
          'printer': 300.00,
          'production': 500.00
        },
        noteRef: "" 
      },
      { 
        id: "environmental-fee",
        description: "Environmental Fee", 
        costs: {
          'mfp-a3': 75.00,
          'mfp-bw-a4': 50.00,
          'scanner': 25.00,
          'printer': 40.00,
          'production': 150.00
        },
        noteRef: "" 
      }
    ],
    generalEquipmentNote: "(Book rates to be used for all other equipment including Scanners & Software)",
    detailedNotes: [
      { noteId: "1", text: "Production units - Subject to change based on a completed site assessment." },
      { noteId: "2", text: "Does not apply to future pick up (i.e. end of term). Additional Charges may apply from the logistics company. Warehouse to confirm prices for production pickups." },
      { noteId: "3", text: "Basic Training ONLY. MFP/Production Training does not include training for imagePRESS/Fiery." },
      { noteId: "4", text: "Install based on connected installation. Production Units - Subject to change based on accessory configuration sold. Please contact your RSM for final approval." }
    ]
  },
  additionalDeliveryFees: {
    title: "Production (Units) - Additional delivery fees to be added based on Sales Rep's site assessment:",
    title: "Production Units - Additional delivery fees may apply based on site inspection and specific requirements.",
    fees: [
      { item: "Stairs (up to 10 stairs)", cost: 1400.00 },
      { item: "Stairs (more than 10 stairs)", cost: 500.00 },
      { item: "Window/Door Removal", cost: 1500.00 },
      { item: "Additional Men due to weight of machine", cost: 800.00 },
      { item: "Forklift/Crane", cost: 2000.00 },
      { item: "Floor Protection", cost: 1500.00 }
    ]
  },
  footerNotes: [
    { title: "Delivery Terms:", text: "Delivery terms are based on the equipment specified on the TranDeal Worksheet. For all other locations, please refer to the transportation Pricebook." },
    { title: "Payment Terms:", text: "Payment terms are Net 30 days from date of invoice, subject to credit approval." },
    { title: "Installation:", text: "Installation will be performed by a Canon authorized technician during standard business hours." },
    { title: "Equipment Installation (Note 4):", text: "Install based on connected installation. Production Units - Subject to change based on accessory configuration sold. Please contact your RSM for final approval." }, // Repeated from soft costs notes, verify if needed here
    { title: "Price Book Rates to be used for all other equipment including Scanners & Software:", text: "Refer to current Canon Price Book for items not explicitly listed above." },
    { title: "Install based on connected installation. Production Units - Subject to change based on accessory configuration sold. Please contact your RSM for final approval:", text: "This is a critical check point before finalizing any production unit sale."}
    // Add any other distinct footer notes from Excel
  ]
};
