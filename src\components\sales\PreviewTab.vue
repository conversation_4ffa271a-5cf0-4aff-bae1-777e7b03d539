<script setup lang="ts">
import type { WorkSheetProduct } from '@/lib/common/types';
import { ref, onMounted, defineExpose, watch, computed, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { getServiceForm, getSalesRequest } from '@/services/salesRequestService';
import ServiceRequestPreview from './ServiceRequestPreview.vue';
import FileManagement from '@/components/files/FileManagement.vue';
import { useRoute } from 'vue-router';
import { getLov, LovCategories, type LovItem } from '@/services/lovService';
import StatusBadge from '../core/StatusBadge.vue';
import { DsdRequestStatus } from '@/enums/DsdRequestStatus';

const { t, locale } = useI18n();


// Dropdown option refs for LOV-driven fields
const channelOptions = ref<{ title: string; value: string }[]>([]);
const portfolioOptions = ref<{ title: string; value: string }[]>([]);
const branchOptions = ref<{ title: string; value: string }[]>([]);
const incumbentOptions = ref<{ title: string; value: string }[]>([]);

onMounted(async () => {
    const lovPromises = [
        getLov(LovCategories.SALES_CHANNEL),
        getLov(LovCategories.PORTFOLIO),
        getLov(LovCategories.BRANCH),
        getLov(LovCategories.CURRENT_INCUMBENT)
    ];
    try{
        const [channelLovs, portfolioLovs, branchLovs,incumbentLovs] = await Promise.all(lovPromises);
       channelOptions.value = channelLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));
        portfolioOptions.value = portfolioLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));
        branchOptions.value = branchLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));
        incumbentOptions.value = incumbentLovs.map((l: LovItem) => ({ title: l.description, value: l.lookupCode }));

    }
    catch {

    }
})
// Define props - only keeping requestId
const props = defineProps({
  requestId: {
    type: [Number, String],
    required: false
  },
  isPnlApproved: {
    type: [String],
    required: false
  }
});

const route = useRoute();
const isEditMode = computed(() => route.name === 'pageEditSalesRequest' || (route.name === 'pageProfitLossCalculator' && props.isPnlApproved=='N'));

// API data state
const apiData = ref<any>({});
const isLoadingApiData = ref(false);
const apiDataError = ref('');

// Computed properties for API data sections
const customerDetails = computed(() => apiData.value.customerDetails || {});
const paymentDetails = computed(() => apiData.value.paymentDetails || {});
const equipmentDetails = computed(() => apiData.value.equipmentDetails || {});
const worksheetDetails = computed(() => apiData.value.worksheetDetails || []);
// const serviceRequestData = computed(() => apiData.value.serviceRequestData || {
//   service_request_form: {},
//   toner_and_service_value_pack: {},
//   approvals_details: {},
//   product_service_details: { models_overview: [], accessories_included: [] },
//   competitive_current_usage_info: {},
//   current_equipment_details: {},
//   service_business_case: {}
// });
const serviceFormId = computed(() => apiData.value.serviceFormId || null);

// Initialize expanded state for worksheet products
watch(() => worksheetDetails.value, (newWorksheetDetails) => {
  if (newWorksheetDetails && Array.isArray(newWorksheetDetails)) {
    newWorksheetDetails.forEach(product => {
      product._expanded = false;
    });
  }
}, { immediate: true });

// Accordion expansion state (Vuetify's v-expansion-panels expects number | null or array, NOT boolean)
// Each section has only one <v-expansion-panel>, so use 0 to indicate expanded, null collapsed.
const customerPanel = ref<number | null>(null); 
const paymentPanel = ref<number | null>(null);
const equipmentPanel = ref<number | null>(null);
const worksheetPanel = ref<number | null>(null);
const serviceRequestPanel = ref<number | null>(null);

// Print functionality
const isPrintLoading = ref(false);

// Service request form data state (separate from API serviceRequestData)
const serviceFormData = ref({
  serviceFormId: null,
  requestId: null,
  servicePackId: null,
  tonerType: null,
  tonerTypeValue: null,
  servicePackDescription: null,
  region: null,
  territory: null,
  coterm: null,
  isDealerAcceptedSr: null,
  paymentMode: null,
  paymentModeValue: null,
  leaseTermInMonth: null,
  msrp: null,
  msrpPercent: null,
  currentUsageInformation: null,
  currentEquipmentInfo: null,
  serviceBusinessCase: null,
  serviceApprovals: [],
  dsdRequestMfpPricing: []
});

const isServiceRequestLoading = ref(false);
const serviceRequestLoadError = ref('');
const hasServiceRequestLoaded = ref(false);

// Function to fetch service form data
const fetchServiceFormData = async () => {
  if (!serviceFormId.value || hasServiceRequestLoaded.value || isServiceRequestLoading.value) {
    return;
  }

  isServiceRequestLoading.value = true;
  serviceRequestLoadError.value = '';

  try {
    const response = await getServiceForm(Number(serviceFormId.value));
    serviceFormData.value = response.data || serviceFormData.value;
    hasServiceRequestLoaded.value = true;
  } catch (error) {
    console.error('Failed to fetch service form data:', error);
    serviceRequestLoadError.value = 'Failed to load service request data';
  } finally {
    isServiceRequestLoading.value = false;
  }
};

// Function to fetch all sales request data from API
const fetchSalesRequestData = async () => {
  if (!props.requestId) {
    console.warn('No request ID provided to fetch sales request data');
    return;
  }

  isLoadingApiData.value = true;
  apiDataError.value = '';

  try {
    const response = await getSalesRequest(Number(props.requestId));
    const data = response.data;

    // Map API response to our data structure
    apiData.value = {
      customerDetails: data || {},
      paymentDetails: data || {},
      equipmentDetails: data || {},
      worksheetDetails: data.requestItems || [],
      serviceRequestData: data.serviceRequestData || {
        service_request_form: {},
        toner_and_service_value_pack: {},
        approvals_details: {},
        product_service_details: { models_overview: [], accessories_included: [] },
        competitive_current_usage_info: {},
        current_equipment_details: {},
        service_business_case: {}
      },
      serviceFormId: data.serviceFormId || null
    };

    // Also fetch service form data if serviceFormId is available
    if (data.serviceFormId) {
      await fetchServiceFormData();
    }

  } catch (error) {
    console.error('Error fetching sales request data:', error);
    apiDataError.value = 'Failed to load sales request data';
  } finally {
    isLoadingApiData.value = false;
  }
};

// Watch for requestId changes to refetch data
watch(() => props.requestId, (newRequestId) => {
  if (newRequestId) {
    fetchSalesRequestData();
  }
}, { immediate: true });

// Watch for service request panel expansion to trigger data loading
watch(() => serviceRequestPanel.value, (newValue) => {
  // newValue is 0 when expanded, null/undefined when collapsed
  if (newValue === 0 && serviceFormId.value) {
    fetchServiceFormData();
  }
});

// Also watch for serviceFormId changes
watch(() => serviceFormId.value, (newValue) => {
  if (newValue && serviceRequestPanel.value === 0) {
    hasServiceRequestLoaded.value = false; // Reset to allow refetch
    fetchServiceFormData();
  }
});

// Format currency values
const formatCurrency = (value: number | null | undefined) => {
  if (!value && value !== 0) return t('previewTab.na');
  return new Intl.NumberFormat(locale.value || 'en-US', { style: 'currency', currency: 'USD' }).format(value);
};

// Transform flat worksheet data into hierarchical structure
const hierarchicalWorksheetData = computed(() => {
  if (!worksheetDetails.value || worksheetDetails.value.length === 0) {
    return [];
  }

  // Group items by parentItemId
  const mainUnits = worksheetDetails.value.filter((item: any) => item.parentItemId === null);
  const subUnits = worksheetDetails.value.filter((item: any) => item.parentItemId !== null);

  // Create hierarchical structure
  return mainUnits.map((mainUnit: { itemId: any; }) => {
    const children = subUnits.filter((subUnit: { parentItemId: any; }) => subUnit.parentItemId === mainUnit.itemId);
    return {
      ...mainUnit,
      subProducts: children,
      _expanded: true // Initially expanded as per requirement
    };
  });
});

// Format percentage values
const formatPercentage = (value: number | null | undefined) => {
  if (value === null || value === undefined) return 'N/A';
  const num = Number(value);
  if (isNaN(num)) return 'N/A';
  return `${num.toFixed(2)}%`;
};

// Compute % of MSRP safely (returns null if MSRP is 0 or missing)
const computePercentOfMsrp = (msrp: number | null | undefined, requested: number | null | undefined): number | null => {
  const msrpNum = Number(msrp);
  const reqNum = Number(requested);
  if (!msrpNum || msrpNum <= 0) return null;
  if (Number.isNaN(reqNum)) return null;
  return (reqNum / msrpNum) * 100;
};

// Format array values for display
const formatArray = (arr: any[] | null | undefined) => {
  if (!arr || !Array.isArray(arr) || arr.length === 0) return 'None';
  return arr.join(', ');
};

// Format object values for display
const formatValue = (value: any) => {
  if (value === null || value === undefined) return 'N/A';
  if (Array.isArray(value)) return formatArray(value);
  if (typeof value === 'object' && value.value !== undefined) return value.value || 'N/A';
  return value || 'N/A';
};

// Format object values for display
const formatIncumbent = (value: string) => {
  if (!value) return 'N/A';

  const keys = value.split(',').map(key => key.trim()); // Split and trim whitespace
  const titles = keys.map(key => {
    const incumbent = incumbentOptions.value.find(option => option.value === key);
    return incumbent ? incumbent.title : key; // Return title if found, otherwise return original key
  });

  return titles.join(', ');
};

const fomAddress =(location:any)=>{
  if (!location) return 'N/A';
  return location.addressLine1 + ', ' + location.city + ', '+ location.country + ', ' + location.state + ', ' + location.postalCode;
}
// Helper function to format boolean values
const formatBooleanValue = (value: any): string => {
  if (typeof value === 'string') {
    return value=='Y' ? 'Yes' : 'No';
  }
  return formatValue(value);
};

// Helper function to format date values
const formatDate = (dateValue: any): string => {
  if (!dateValue && dateValue !== 0) return t('previewTab.na');
  try {
    let date: Date;
    if (dateValue instanceof Date) {
      date = dateValue;
    } else if (typeof dateValue === 'string') {
      date = new Date(dateValue);
    } else if (typeof dateValue === 'object' && dateValue.value) {
      date = new Date(dateValue.value);
    } else {
      date = new Date(dateValue);
    }
    if (isNaN(date.getTime())) return t('previewTab.na');
    return new Intl.DateTimeFormat(locale.value || 'en-US', { year: 'numeric', month: '2-digit', day: '2-digit' }).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return t('previewTab.na');
  }
};

// Computed properties for worksheet totals
const solutionTotals = computed(() => {
  if (!hierarchicalWorksheetData.value || hierarchicalWorksheetData.value.length === 0) {
    return { dsdQuantity: 0, dealerQuantity: 0, msrp: 0, requestedPrice: 0 };
  }

  let totals = { dsdQuantity: 0, dealerQuantity: 0, msrp: 0, requestedPrice: 0 };

  hierarchicalWorksheetData.value.forEach((mainUnit: { isSolution: string; dsdQuantity: any; dealerIt: any; msrp: any; requestedSellingPrice: any; subProducts: any[]; }) => {
    // Include main unit if it's a solution
    if (mainUnit.isSolution === 'Y') {
      const dsdQty = Number(mainUnit.dsdQuantity) || 0;
      const dealerQty = Number(mainUnit.dealerIt) || 0;
      const msrp = Number(mainUnit.msrp) || 0;
      const requestedPrice = Number(mainUnit.requestedSellingPrice) || 0;

      totals.dsdQuantity += dsdQty;
      totals.dealerQuantity += dealerQty;
      totals.msrp += msrp * (dsdQty + dealerQty);
      totals.requestedPrice += requestedPrice * (dsdQty + dealerQty);
    }

    // Include sub-products if they are solutions
    if (mainUnit.subProducts && mainUnit.subProducts.length > 0) {
      mainUnit.subProducts.forEach((subUnit: { isSolution: string; dsdQuantity: any; dealerIt: any; msrp: any; requestedSellingPrice: any; }) => {
        if (subUnit.isSolution === 'Y') {
          const dsdQty = Number(subUnit.dsdQuantity) || 0;
          const dealerQty = Number(subUnit.dealerIt) || 0;
          const msrp = Number(subUnit.msrp) || 0;
          const requestedPrice = Number(subUnit.requestedSellingPrice) || 0;

          // totals.dsdQuantity += dsdQty;
          // totals.dealerQuantity += dealerQty;
          totals.msrp += msrp * (dsdQty + dealerQty);
          totals.requestedPrice += requestedPrice * (dsdQty + dealerQty);
        }
      });
    }
  });

  return totals;
});

const hardwareTotals = computed(() => {
  if (!hierarchicalWorksheetData.value || hierarchicalWorksheetData.value.length === 0) {
    return { dsdQuantity: 0, dealerQuantity: 0, msrp: 0, requestedPrice: 0 };
  }

  let totals = { dsdQuantity: 0, dealerQuantity: 0, msrp: 0, requestedPrice: 0 };
  hierarchicalWorksheetData.value.forEach((mainUnit: { isMainframe: string; isSolution: string; dsdQuantity: any; dealerIt: any; msrp: any; requestedSellingPrice: any; subProducts: any[]; }) => {
    // Include main unit if it's hardware
    if (mainUnit.isMainframe === 'Y' && mainUnit.isSolution === 'N') {
      const dsdQty = Number(mainUnit.dsdQuantity) || 0;
      const dealerQty = Number(mainUnit.dealerIt) || 0;
      const msrp = Number(mainUnit.msrp) || 0;
      const requestedPrice = Number(mainUnit.requestedSellingPrice) || 0;

      totals.dsdQuantity += dsdQty;
      totals.dealerQuantity += dealerQty;
      totals.msrp += msrp * (dsdQty + dealerQty);
      totals.requestedPrice += requestedPrice * (dsdQty + dealerQty);
    }

    // Include sub-products if they are hardware
    if (mainUnit.subProducts && mainUnit.subProducts.length > 0) {
      mainUnit.subProducts.forEach((subUnit: { isSolution: string; dsdQuantity: any; dealerIt: any; msrp: any; requestedSellingPrice: any; }) => {
        if (subUnit.isSolution === 'N') {
          const dsdQty = Number(subUnit.dsdQuantity) || 0;
          const dealerQty = Number(subUnit.dealerIt) || 0;
          const msrp = Number(subUnit.msrp) || 0;
          const requestedPrice = Number(subUnit.requestedSellingPrice) || 0;

          // totals.dsdQuantity += dsdQty;
          // totals.dealerQuantity += dealerQty;
          totals.msrp += msrp * (dsdQty + dealerQty);
          totals.requestedPrice += requestedPrice * (dsdQty + dealerQty);
        }
      });
    }
  });

  return totals;
});

const grandTotals = computed(() => {
  return {
    dsdQuantity: solutionTotals.value.dsdQuantity + hardwareTotals.value.dsdQuantity,
    dealerQuantity: solutionTotals.value.dealerQuantity + hardwareTotals.value.dealerQuantity,
    msrp: solutionTotals.value.msrp + hardwareTotals.value.msrp,
    requestedPrice: solutionTotals.value.requestedPrice + hardwareTotals.value.requestedPrice
  };
});

// Summary % of MSRP calculations
const hardwareMsrpPercentage = computed(() => {
  const msrp = hardwareTotals.value.msrp || 0;
  const requested = hardwareTotals.value.requestedPrice || 0;
  return msrp > 0 ? (requested / msrp) * 100 : null;
});

const solutionMsrpPercentage = computed(() => {
  const msrp = solutionTotals.value.msrp || 0;
  const requested = solutionTotals.value.requestedPrice || 0;
  return msrp > 0 ? (requested / msrp) * 100 : null;
});

const grandMsrpPercentage = computed(() => {
  const msrp = grandTotals.value.msrp || 0;
  const requested = grandTotals.value.requestedPrice || 0;
  return msrp > 0 ? (requested / msrp) * 100 : null;
});

// Computed properties for MFP Incumbents table
const uniqueVendors = computed(() => {
  if (!equipmentDetails.value?.mfpIncumbents) return [];

  const vendors = new Set<string>();
  equipmentDetails.value.mfpIncumbents.forEach((item: any) => {
    vendors.add(item.currentIncumbent);
  });

  return Array.from(vendors).sort();
});

const processedMfpData = computed(() => {
  if (!equipmentDetails.value?.mfpIncumbents) return [];

  const groupedData: Record<string, any> = {};

  // Group by competitionType
  equipmentDetails.value.mfpIncumbents.forEach((item: any) => {
    const type = item.competitionType;

    if (!groupedData[type]) {
      groupedData[type] = {
        type,
        totalUnits: 0,
        vendors: {}
      };
    }

    // Add units
    groupedData[type].totalUnits += item.noOfUnit || 0;

    // Add vendor percentage
    const vendor = item.currentIncumbent;
    if (!groupedData[type].vendors[vendor]) {
      groupedData[type].vendors[vendor] = 0;
    }
    groupedData[type].vendors[vendor] += item.incumbentPercent || 0;
  });

  return Object.values(groupedData);
});

// Format vendor names for display using incumbentOptions
const formatVendorName = (vendor: string): string => {
  const incumbent = incumbentOptions.value.find(option => option.value === vendor);
  return incumbent ? incumbent.title : vendor;
};

// PDF Generation function
const generatePDF = async () => {
  isPrintLoading.value = true;

  try {
    // Store original panel states
    const originalStates = {
      customer: customerPanel.value,
      payment: paymentPanel.value,
      equipment: equipmentPanel.value,
      worksheet: worksheetPanel.value,
      serviceRequest: serviceRequestPanel.value
    };

    // Open all panels
    customerPanel.value = 0;
    paymentPanel.value = 0;
    equipmentPanel.value = 0;
    worksheetPanel.value = 0;
    if (apiData.value.serviceFormId) {
      serviceRequestPanel.value = 0;
    }

    // Wait for DOM to update
    await nextTick();

    // Wait a bit more for animations to complete
    await new Promise(resolve => setTimeout(resolve, 500));

    // Import html2pdf dynamically
    const html2pdf = (await import('html2pdf.js')).default;

    // Get the preview content element
    const element = document.querySelector('.preview-content') as HTMLElement;

    if (!element) {
      throw new Error('Preview content not found');
    }

    // PDF options - configured for text-based PDF instead of image
    const options = {
      margin: 0.5,
      filename: `sales-request-preview-${props.requestId || 'draft'}.pdf`,
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: {
        scale: 1, // Reduced scale for better text rendering
        useCORS: true,
        letterRendering: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
        // Disable image rendering to force text-based content
        ignoreElements: (element: Element) => {
          // Skip rendering of certain decorative elements that might cause image conversion
          return element.classList.contains('v-expansion-panel-title__overlay') ||
                 element.classList.contains('v-ripple__container');
        }
      },
      jsPDF: {
        unit: 'in',
        format: 'a4',
        orientation: 'portrait',
        compress: true,
        // Enable text mode for better text selection
        putOnlyUsedFonts: true,
        floatPrecision: 16
      },
      pagebreak: {
        mode: ['avoid-all', 'css', 'legacy'],
        before: '.page-break-before',
        after: '.page-break-after',
        avoid: '.no-page-break'
      }
    };

    // Generate PDF
    await html2pdf().set(options).from(element).save();

    // Restore original panel states
    customerPanel.value = originalStates.customer;
    paymentPanel.value = originalStates.payment;
    equipmentPanel.value = originalStates.equipment;
    worksheetPanel.value = originalStates.worksheet;
    serviceRequestPanel.value = originalStates.serviceRequest;

  } catch (error) {
    console.error('Error generating PDF:', error);
    // You might want to show a snackbar error message here
  } finally {
    isPrintLoading.value = false;
  }
};

// Expose methods for parent component
defineExpose({
  refreshData: async () => {
    // Force refetch both sales request and service form data when tab is activated
    hasServiceRequestLoaded.value = false; // allow service form refetch
    await fetchSalesRequestData(); // this will call getSalesRequest and then fetchServiceFormData()
  }
});
// Computed property to determine if all panels are expanded
const allPanelsExpanded = computed<boolean>({
  get() {
    return (
      customerPanel.value === 0 &&
      paymentPanel.value === 0 &&
      equipmentPanel.value === 0 &&
      worksheetPanel.value === 0 &&
      serviceRequestPanel.value === 0
    );
  },
  set(val: boolean) {
    const panelState = val ? 0 : null;
    customerPanel.value = panelState;
    paymentPanel.value = panelState;
    equipmentPanel.value = panelState;
    worksheetPanel.value = panelState;
    serviceRequestPanel.value = panelState;
  }
});


</script>

<template>
  <v-container fluid class="preview-content">
    <v-row class="mb-2" align="center" justify="end">
      <v-col>
      <span v-if="!route.path.includes('profit-loss-calculator')"> 
        <strong>{{ t('previewTab.status.status') }}</strong> <StatusBadge :status="customerDetails.dsdRequestStatus==DsdRequestStatus.PD_APPROVAL_PENDING && isPnlApproved=='Y' ? DsdRequestStatus.APPROVAL_PRICING_PENDING : customerDetails.dsdRequestStatus"/> &nbsp;&nbsp;
      </span>
        <strong>{{ t('previewTab.status.salesRep') }}</strong> {{ formatValue(customerDetails?.salesRepName) }}&nbsp;&nbsp;
      <strong>{{ t('previewTab.status.pdAnalyst') }}</strong> {{ formatValue(customerDetails?.pdAssignmentName) }}

      </v-col>

      <v-col cols="auto">
        <v-checkbox
          v-model="allPanelsExpanded"
          :label="t('previewTab.expandAll')"
          hide-details
          density="compact"
          class="ma-0 pa-0"

        />
      </v-col>
        <v-col cols="auto">
          <v-btn
            color="primary"
            variant="outlined"
            @click="generatePDF"
            :loading="isPrintLoading"
          >
            {{ t('previewTab.print') }}
          </v-btn>
        </v-col>
    </v-row>
    <!-- Customer Details Section -->
    <v-expansion-panels v-model="customerPanel">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">person</v-icon>
            <span class="text-h6">{{ t('previewTab.sections.customer') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <v-table density="compact">
            <tbody>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.businessName') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.customer?.businessName) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.legalName') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.customer?.legalName) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.sfOpportunityId') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.sfOpportunityId) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.relationship') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.customer?.relationshipStatus) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.rfp') }}</th>
                <td class="text-left">{{ formatBooleanValue(customerDetails?.isRfp) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.rfpDueDate') }}</th>
                <td class="text-left">{{ formatDate(customerDetails?.rfpDueDate) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.location') }}</th>
                <td class="text-left">{{ fomAddress(customerDetails?.location) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.website') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.customer?.website) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.globalAgreement') }}</th>
                <td class="text-left">{{ formatBooleanValue(customerDetails?.customer?.isGlobalAgreement) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.branch') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.salesBranch) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.salesRep') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.salesRepName) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.salesManager') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.salesManagerName) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.regionalLeader') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.regionalLeaderName) }}</td>
              </tr>
              <tr >
                <th class="text-left">{{ t('previewTab.customer.labels.pdAnalyst') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.pdAssignmentName) }}</td>
              </tr>
              <!-- <tr>
                <th class="text-left">Sales Channel</th>
                <td class="text-left">{{ formatValue(customerDetails?.salesChannelValue) }}</td>
              </tr> -->
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.portfolio') }}</th>
                <td class="text-left">{{ formatValue(customerDetails?.portfolio?.name) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.printAssessment') }}</th>
                <td class="text-left">{{ formatBooleanValue(customerDetails?.printAssessment) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.coreItsNetworkScan') }}</th>
                <td class="text-left">{{ formatBooleanValue(customerDetails?.networkScan) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.customer.labels.msa') }}</th>
                <td class="text-left">{{ formatBooleanValue(customerDetails?.isMsa) }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Payment Details Section -->
    <v-expansion-panels v-model="paymentPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">payments</v-icon>
            <span class="text-h6">{{ t('previewTab.sections.payment') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <v-table density="compact">
            <tbody>
              <tr>
                <th class="text-left">{{ t('previewTab.payment.labels.pricingCategory') }}</th>
                <td class="text-left">{{ formatValue(paymentDetails.pricingCategoryCode) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.payment.labels.minimumCommitment') }}</th>
                <td class="text-left">{{ formatCurrency(paymentDetails.minimumCommitmentAmount) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.payment.labels.paymentMode') }}</th>
                <td class="text-left">{{ formatValue(paymentDetails.paymentMode) }}</td>
              </tr>
              <tr v-if="paymentDetails.paymentJustification">
                <th class="text-left">{{ t('previewTab.payment.labels.paymentJustification') }}</th>
                <td class="text-left">{{ formatValue(paymentDetails.paymentJustification) }}</td>
              </tr>
              <tr v-if="paymentDetails.leaseTermInMonth">
                <th class="text-left">{{ t('previewTab.payment.labels.leaseTermMonths') }}</th>
                <td class="text-left">{{ formatValue(paymentDetails.leaseTermInMonth) }}</td>
              </tr>
              <tr v-if="paymentDetails.typeOfContract">
                <th class="text-left">{{ t('previewTab.payment.labels.typeOfContract') }}</th>
                <td class="text-left">{{ formatValue(paymentDetails.typeOfContract) }}</td>
              </tr>
              <tr v-if="paymentDetails.billingPeriod">
                <th class="text-left">{{ t('previewTab.payment.labels.billingPeriod') }}</th>
                <td class="text-left">{{ formatValue(paymentDetails.billingPeriod) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.payment.labels.supplierType') }}</th>
                <td class="text-left">{{ formatValue(paymentDetails.supplierType) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.payment.labels.implementation') }}</th>
                <td class="text-left">{{ formatValue(paymentDetails.implementation) }}</td>
              </tr>
              <tr>
                <th class="text-left">Foreign Buyout/Trade Up</th>
                <td class="text-left">{{ formatCurrency(paymentDetails.foreignBuyout) }}</td>
              </tr>
              <tr>
                <th class="text-left">Proposal Date</th>
                <td class="text-left">{{ formatDate(paymentDetails.proposalDate) }}</td>
              </tr>
            </tbody>
          </v-table>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Equipment Details Section -->
    <v-expansion-panels v-model="equipmentPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">devices</v-icon>
            <span class="text-h6">{{ t('previewTab.sections.equipment') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <!-- Equipment Basic Info -->
       

          <!-- Strategy Information -->
          <v-table density="compact" class="mb-4">
            <tbody>
                 <tr>
                <th class="text-left">{{ t('previewTab.equipment.labels.currentIncumbent') }}</th>
                <td class="text-left">{{ formatIncumbent(equipmentDetails?.currentIncumbent) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.equipment.labels.potentialUnits') }}</th>
                <td class="text-left">{{ formatValue(equipmentDetails.potentialUnitCount) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.equipment.labels.installationDate') }}</th>
                <td class="text-left">{{ formatDate(equipmentDetails.installationDate) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.equipment.labels.specialConsiderations') }}</th>
                <td class="text-left">{{ formatValue(equipmentDetails.specialConsideration) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.equipment.labels.hardwarePricingStrategy') }}</th>
                <td class="text-left">{{ formatValue(equipmentDetails.hardwarePricingStrategy) }}</td>
              </tr>
              <tr>
                <th class="text-left">{{ t('previewTab.equipment.labels.softwareSalesStrategy') }}</th>
                <td class="text-left">{{ formatValue(equipmentDetails.softwareSalesStrategy) }}</td>
              </tr>
           
            </tbody>
          </v-table>

          <!-- Equipment Distribution Table -->
          <h3 class="text-subtitle-1 mb-2">{{ t('previewTab.equipment.labels.equipmentDistribution') }}</h3>

          <v-table density="compact" class="bordered-table" v-if="equipmentDetails?.mfpIncumbents">
            <thead>
              <tr>
                <th class="border-right"></th>
                <th class="border-right text-center">{{ t('previewTab.equipment.labels.numberOfUnits') }}</th>
                <th v-for="vendor in uniqueVendors" :key="vendor" class="text-center border-right">
                  {{ formatVendorName(vendor) }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="category in processedMfpData" :key="category.type">
                <td class="border-right font-weight-medium">{{ category.type }}</td>
                <td class="border-right text-center">{{ category.totalUnits }}</td>
                <td v-for="vendor in uniqueVendors" :key="vendor" class="text-center border-right">
                  {{ category.vendors[vendor] || '0' }}%
                </td>
              </tr>
            </tbody>
          </v-table>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Worksheet Details Section -->
    <v-expansion-panels v-model="worksheetPanel" class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">description</v-icon>
            <span class="text-h6">{{ t('previewTab.sections.worksheet') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <div v-if="hierarchicalWorksheetData?.length > 0">
            <!-- Main products table with expandable rows -->
            <v-table density="compact" class="mb-4">
              <thead>
                <tr>
                  <th width="40" class="text-center"></th>
                  <th class="text-left">{{ t('previewTab.worksheet.headers.itemId') }}</th>
                  <th class="text-left">{{ t('previewTab.worksheet.headers.displayName') }}</th>
                  <th class="text-left">{{ t('previewTab.worksheet.headers.dsdQuantity') }}</th>
                  <th class="text-left">{{ t('previewTab.worksheet.headers.dealerQuantity') }}</th>
                  <th class="text-left">{{ t('previewTab.worksheet.headers.msrp') }}</th>
                  <th class="text-left">{{ t('previewTab.worksheet.headers.requestedPrice') }}</th>
                  <th class="text-left">{{ t('previewTab.worksheet.headers.percentageOfMSRP') }}</th>
                  <th class="text-left">{{ t('previewTab.worksheet.headers.type') }}</th>
                </tr>
              </thead>
              <tbody>
                <template v-for="(mainUnit, mainIndex) in hierarchicalWorksheetData" :key="mainIndex">
                  <!-- Main Unit Row -->
                  <tr class="main-unit-row">
                    <td class="text-center">
                      <v-icon
                        v-if="mainUnit.subProducts && mainUnit.subProducts.length > 0"
                        size="small"
                        :color="mainUnit.isSolution === 'Y' ? 'blue' : 'green'"
                      >
                        expand_less
                      </v-icon>
                    </td>
                    <td class="font-weight-bold">{{ mainUnit.itemId }}</td>
                    <td class="font-weight-bold" :class="{ 'text-blue': mainUnit.isSolution === 'Y', 'text-green': mainUnit.isSolution === 'N' }">
                      {{ mainUnit.displayName }}
                      <span v-if="mainUnit.isPrimaryProposal" class="text-caption text-medium-emphasis ml-1">
                        ({{ mainUnit.isPrimaryProposal === 'Y' ? 'Primary' : 'Optional' }})
                      </span>
                    </td>
                    <td class="font-weight-bold">{{ mainUnit.isSolution === 'N' ? (mainUnit.dsdQuantity || 0) : '-' }}</td>
                    <td class="font-weight-bold">{{ mainUnit.isSolution === 'N' ? (mainUnit.dealerIt || 0) : '-' }}</td>
                    <td class="font-weight-bold">{{ formatCurrency(mainUnit.msrp) }}</td>
                    <td class="font-weight-bold">{{ formatCurrency(mainUnit.requestedSellingPrice) }}</td>
                    <td class="font-weight-bold">{{ formatPercentage(computePercentOfMsrp(mainUnit.msrp, mainUnit.requestedSellingPrice)) }}</td>
                    <td>
                      <v-chip
                        size="small"
                        :color="mainUnit.isSolution === 'Y' ? 'blue' : 'green'"
                        variant="tonal"
                      >
                        {{ mainUnit.isSolution === 'Y' ? 'Solution' : 'Hardware' }}
                      </v-chip>
                    </td>
                  </tr>

                  <!-- Sub Units Rows (Always shown since _expanded is true) -->
                  <template v-if="mainUnit.subProducts && mainUnit.subProducts.length > 0">
                    <tr
                      v-for="subUnit in mainUnit.subProducts"
                      :key="subUnit.requestItemId"
                      class="sub-unit-row"
                    >
                      <td class="text-center">
                        <v-icon size="small" class="text-grey">subdirectory_arrow_right</v-icon>
                      </td>
                      <td class="pl-4 text-grey-darken-1">{{ subUnit.itemId }}</td>
                      <td class="text-grey-darken-1">{{ subUnit.displayName }}</td>
                      <td class="text-grey-darken-1">{{ subUnit.dsdQuantity || 0 }}</td>
                      <td class="text-grey-darken-1">{{ subUnit.dealerIt || 0 }}</td>
                      <td class="text-grey-darken-1">{{ formatCurrency(subUnit.msrp) }}</td>
                      <td class="text-grey-darken-1">{{ formatCurrency(subUnit.requestedSellingPrice) }}</td>
                      <td class="text-grey-darken-1">{{ formatPercentage(computePercentOfMsrp(subUnit.msrp, subUnit.requestedSellingPrice)) }}</td>
                      <td>
                        <v-chip
                          size="small"
                          :color="subUnit.isSolution === 'Y' ? 'blue' : 'green'"
                          variant="outlined"
                        >
                          {{ subUnit.isSolution === 'Y' ? 'Solution' : 'Hardware' }}
                        </v-chip>
                      </td>
                    </tr>
                  </template>
                </template>
              </tbody>
            </v-table>
          </div>

          <v-alert v-else type="info" class="mt-2">
            {{ t('previewTab.messages.noWorksheetData') }}
          </v-alert>

          <!-- Worksheet Summary Section -->
          <div v-if="hierarchicalWorksheetData && hierarchicalWorksheetData.length > 0" class="mt-6">
            <h3 class="text-h6 mb-4 d-flex align-center">
              <v-icon class="mr-2">summarize</v-icon>
              {{ t('previewTab.worksheet.summary.title') }}
            </h3>

         

            <!-- Hardware Items Summary -->
            <div class="mb-4">
              <h4 class="text-subtitle-1 mb-2 text-green">{{ t('previewTab.worksheet.summary.hardwareTotals') }}</h4>
              <v-row>
                <v-col cols="12" sm="6" md="2">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">DSD Quantity</div>
                    <div class="text-h6 text-green">{{ hardwareTotals.dsdQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="2">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">{{ t('previewTab.worksheet.summary.dealerQuantity') }}</div>
                    <div class="text-h6 text-green">{{ hardwareTotals.dealerQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">{{ t('previewTab.worksheet.summary.msrp') }}</div>
                    <div class="text-h6 text-green">{{ formatCurrency(hardwareTotals.msrp) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">{{ t('previewTab.worksheet.summary.requestedPrice') }}</div>
                    <div class="text-h6 text-green">{{ formatCurrency(hardwareTotals.requestedPrice) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="2">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">{{ t('previewTab.worksheet.summary.percentageOfMSRP') }}</div>
                    <div class="text-h6 text-green">{{ formatPercentage(hardwareMsrpPercentage) }}</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
   <!-- Solution Items Summary -->
            <div class="mb-4">
              <h4 class="text-subtitle-1 mb-2 text-blue">{{ t('previewTab.worksheet.summary.solutionTotals') }}</h4>
              <v-row>
                <v-col cols="12" sm="6" md="2">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">{{ t('previewTab.worksheet.summary.dsdQuantity') }}</div>
                    <div class="text-h6 text-blue">{{ solutionTotals.dsdQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="2">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">{{ t('previewTab.worksheet.summary.dealerQuantity') }}</div>
                    <div class="text-h6 text-blue">{{ solutionTotals.dealerQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">{{ t('previewTab.worksheet.summary.msrp') }}</div>
                    <div class="text-h6 text-blue">{{ formatCurrency(solutionTotals.msrp) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">{{ t('previewTab.worksheet.summary.requestedPrice') }}</div>
                    <div class="text-h6 text-blue">{{ formatCurrency(solutionTotals.requestedPrice) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="2">
                  <v-card variant="outlined" class="pa-3 text-center">
                    <div class="text-caption text-grey-darken-1">{{ t('previewTab.worksheet.summary.percentageOfMSRP') }}</div>
                    <div class="text-h6 text-blue">{{ formatPercentage(solutionMsrpPercentage) }}</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
            <!-- Grand Totals -->
            <div class="mb-4">
              <h4 class="text-subtitle-1 mb-2">{{ t('previewTab.worksheet.summary.grandTotals') }}</h4>
              <v-row>
                <v-col cols="12" sm="6" md="2">
                  <v-card variant="elevated" color="primary" class="pa-3 text-center text-white">
                    <div class="text-caption">{{ t('previewTab.worksheet.summary.dsdQuantity') }}</div>
                    <div class="text-h5 font-weight-bold">{{ grandTotals.dsdQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="2">
                  <v-card variant="elevated" color="primary" class="pa-3 text-center text-white">
                    <div class="text-caption">{{ t('previewTab.worksheet.summary.dealerQuantity') }}</div>
                    <div class="text-h5 font-weight-bold">{{ grandTotals.dealerQuantity.toLocaleString() }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="elevated" color="primary" class="pa-3 text-center text-white">
                    <div class="text-caption">{{ t('previewTab.worksheet.summary.msrp') }}</div>
                    <div class="text-h5 font-weight-bold">{{ formatCurrency(grandTotals.msrp) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="3">
                  <v-card variant="elevated" color="primary" class="pa-3 text-center text-white">
                    <div class="text-caption">{{ t('previewTab.worksheet.summary.requestedPrice') }}</div>
                    <div class="text-h5 font-weight-bold">{{ formatCurrency(grandTotals.requestedPrice) }}</div>
                  </v-card>
                </v-col>
                <v-col cols="12" sm="6" md="2">
                  <v-card variant="elevated" color="primary" class="pa-3 text-center text-white">
                    <div class="text-caption">{{ t('previewTab.worksheet.summary.percentageOfMSRP') }}</div>
                    <div class="text-h5 font-weight-bold">{{ formatPercentage(grandMsrpPercentage) }}</div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
          </div>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

 

    <!-- Service Request Details Section -->
    <v-expansion-panels v-model="serviceRequestPanel" class="mt-4" v-if="apiData.serviceFormId">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">assignment</v-icon>
            <span class="text-h6">{{ t('previewTab.sections.serviceRequest') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <div v-if="isServiceRequestLoading" class="text-center pa-4">
            <v-progress-circular indeterminate color="primary"></v-progress-circular>
            <div class="mt-2">{{ t('previewTab.messages.loadingServiceRequest') }}</div>
          </div>
          <v-alert v-else-if="serviceRequestLoadError" type="error" class="ma-4">
            {{ serviceRequestLoadError }}
          </v-alert>
          <ServiceRequestPreview
            v-else
            :service-request-data="serviceFormData"
          />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
       <!-- Files Section -->
    <v-expansion-panels class="mt-4">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">attachment</v-icon>
            <span class="text-h6">{{ t('previewTab.sections.files') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <FileManagement :request-id="Number(props.requestId)" :is-edit-mode="isEditMode" />
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>

    <!-- Comments Section -->
    <v-expansion-panels class="mt-4" v-if="customerDetails.salesManagerRemark || customerDetails.rlManagerRemark ||customerDetails.priceDeskRemark || customerDetails.sdRemark">
      <v-expansion-panel>
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-icon class="mr-2">comment</v-icon>
            <span class="text-h6">{{ t('previewTab.sections.approvalComments') }}</span>
          </div>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <div class="pa-4">
            <!-- Sales Manager Comments -->
            <div v-if="customerDetails.salesManagerRemark" class="mb-4">
              <h4 class="text-subtitle-1 mb-2 text-primary">{{ t('previewTab.sections.salesManagerComments') }} :{{customerDetails.salesManagerName}}</h4>
              <v-card variant="outlined" class="pa-3">
                <p class="text-body-2">{{ customerDetails.salesManagerRemark }}</p>
              </v-card>
            </div>

            <!-- Regional Manager Comments -->
            <div v-if="customerDetails.rlManagerRemark" class="mb-4">
              <h4 class="text-subtitle-1 mb-2 text-primary">{{ t('previewTab.sections.regionalManagerComments') }}</h4>
              <v-card variant="outlined" class="pa-3">
                <p class="text-body-2">{{ customerDetails.rlManagerRemark }}</p>
              </v-card>
            </div>

              <!-- PD Manager Comments -->
            <div v-if="customerDetails.priceDeskRemark" class="mb-4">
              <h4 class="text-subtitle-1 mb-2 text-primary">{{ t('previewTab.sections.pricedeskComments') }}</h4>
              <v-card variant="outlined" class="pa-3">
                <p class="text-body-2">{{ customerDetails.priceDeskRemark }}</p>
              </v-card>
            </div>

            <!-- SD Manager Comments -->
            <div v-if="customerDetails.sdRemark" class="mb-4">
              <h4 class="text-subtitle-1 mb-2 text-primary">{{ t('previewTab.sections.sdComments') }}</h4>
              <v-card variant="outlined" class="pa-3">
                <p class="text-body-2">{{ customerDetails.sdRemark }}</p>
              </v-card>
            </div>
          </div>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-container>
</template>

<style scoped>



.v-table {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.main-unit-row {
  background-color: rgb(var(--v-theme-background-1)) !important;;
  border-left: 4px solid #4caf50;
}

.main-unit-row.solution {
  border-left-color: #2196f3;
}

.sub-unit-row {
  background-color: rgb(var(--v-theme-background-2)) !important;;
  border-left: 2px solid #e0e0e0;
}

.sub-unit-row td {
  font-size: 0.875rem;
  color: rgb(var(--v-theme-foreground-1)) !important;;
}

.text-blue {
  color: #2196f3 !important;
}

.text-green {
  color: #4caf50 !important;
}

.rotate-45 {
  transform: rotate(45deg);
  transition: transform 0.2s;
}
</style>









