<template>
  <v-navigation-drawer
    v-model="isOpen"
    location="right"
    temporary
    width="500"
    class="approval-notes-panel"
  >
    <v-card flat class="h-100">
      <v-card-title class="d-flex align-center justify-space-between pa-4 bg-primary text-white">
        <span class="text-h6">PD Approval Notes</span>
        <v-btn
          icon="mdi-close"
          variant="text"
          color="white"
          size="small"
          @click="closePanel"
        />
      </v-card-title>

      <v-card-text class="pa-0">
        <v-list class="py-0">
          <template v-for="(notes, category) in approvalNotes" :key="category">
            <!-- Category Header -->
            <v-list-subheader class="text-subtitle-2 font-weight-bold text-primary px-4 py-3">
              {{ category }}
            </v-list-subheader>
            
            <!-- Category Items -->
            <template v-for="note in notes" :key="note.id">
              <v-list-item class="px-4 py-2">
                <template v-slot:prepend>
                  <v-icon size="small" color="primary" class="mr-3">
                    mdi-circle-small
                  </v-icon>
                </template>
                
                <v-list-item-title class="text-body-2 text-wrap pr-2">
                  {{ note.text }}
                </v-list-item-title>
                
                <template v-slot:append>
                  <v-btn
                    icon="mdi-content-copy"
                    variant="text"
                    size="small"
                    color="primary"
                    @click="copyToClipboard(note.text)"
                    :title="`Copy: ${note.text.substring(0, 50)}...`"
                  />
                </template>
              </v-list-item>
              
              <v-divider v-if="note.id !== notes[notes.length - 1].id" class="mx-4" />
            </template>
            
            <!-- Category Divider -->
            <v-divider 
              v-if="category !== Object.keys(approvalNotes)[Object.keys(approvalNotes).length - 1]" 
              class="my-2" 
              thickness="2"
            />
          </template>
        </v-list>
      </v-card-text>
    </v-card>

    <!-- Success Snackbar -->
    <v-snackbar
      v-model="showCopySuccess"
      timeout="2000"
      color="success"
      location="bottom"
    >
      <v-icon class="mr-2">mdi-check-circle</v-icon>
      Text copied to clipboard!
    </v-snackbar>
  </v-navigation-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { approvalNotes } from '@/data/approval-notes';

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>();

// Local state
const showCopySuccess = ref(false);

// Computed
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// Methods
const closePanel = () => {
  isOpen.value = false;
};

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
    showCopySuccess.value = true;
  } catch (error) {
    console.error('Failed to copy text:', error);
    // Fallback for older browsers
    fallbackCopyTextToClipboard(text);
  }
};

const fallbackCopyTextToClipboard = (text: string) => {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  textArea.style.top = '0';
  textArea.style.left = '0';
  textArea.style.position = 'fixed';
  
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  
  try {
    document.execCommand('copy');
    showCopySuccess.value = true;
  } catch (error) {
    console.error('Fallback: Could not copy text:', error);
  }
  
  document.body.removeChild(textArea);
};
</script>

<style scoped>
.approval-notes-panel :deep(.v-list-item-title) {
  white-space: normal !important;
  line-height: 1.4;
}

.approval-notes-panel :deep(.v-list-item) {
  min-height: auto;
  padding-top: 8px;
  padding-bottom: 8px;
}

.approval-notes-panel :deep(.v-list-subheader) {
  background-color: rgba(var(--v-theme-surface-variant), 0.1);
  border-left: 4px solid rgb(var(--v-theme-primary));
}
</style>
