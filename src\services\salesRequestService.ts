import axios, { type AxiosResponse } from 'axios';
import { useAppStore } from '@/stores/AppStore';
import { useUserStore } from '@/stores/UserStore';
import getAccessToken from '@/composables/auth/getAccessToken.js';

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add a request interceptor to include the auth token from the app store
// Request interceptor – attaches auth token **and** starts global loader
apiClient.interceptors.request.use(
    (config) => {
    const appStore = useAppStore();

    // ----- Authentication token -----
    const token = appStore.authenticationToken;
    if (token) {
      // Ensure header object exists first
      if (!config.headers) config.headers = {};
      config.headers.Authorization = `Bearer ${token}`;
    }

    // ----- Global loader handling -----
    // Generate a unique loader ID per request
      const loaderId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
    // Optional: use a custom message supplied on the request config
    // e.g., axios.get(url, { loaderMessage: 'Fetching data' })
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const loaderMessage: string | undefined = config.loaderMessage;
      appStore.startLoader(loaderId, loaderMessage);

    // Persist the loaderId on the config so that the response interceptor can access it
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore – augmenting AxiosRequestConfig dynamically
      config.loaderId = loaderId;

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor – stops the corresponding loader once the request is done (success or error)
apiClient.interceptors.response.use(
  (response) => {
    const appStore = useAppStore();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const loaderId = response.config.loaderId as string | undefined;
    if (loaderId) {
      appStore.stopLoader(loaderId);
    }
    return response;
  },
  (error) => {
    const appStore = useAppStore();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const loaderId = error.config?.loaderId as string | undefined;
    if (loaderId) {
      appStore.stopLoader(loaderId);
    }
    return Promise.reject(error);
  }
);

export const saveSalesRequest = async (data: any) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  return apiClient.post('/dsd/request/save', data, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

export const updateSalesApprovers = async (requestId:string,data: any) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  
  // Build query parameters dynamically
  const queryParams = new URLSearchParams();
  if (data.regionalLeaderOid) queryParams.append('regionalLeaderOid', data.regionalLeaderOid);
  if (data.salesManagerOid) queryParams.append('salesManagerOid', data.salesManagerOid);
  if (data.pdAssignmentUserId) queryParams.append('pdAssignmentUserId', data.pdAssignmentUserId);
  
  return apiClient.patch(`/dsd/request/${requestId}?${queryParams.toString()}`, null, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

export const validateRequest = async (requestId: any) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  return apiClient.get(`/dsd/request/validate/${requestId}`, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

export const submitSalesRequest = async (requestId: any) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  return apiClient.post(`/dsd/request/submit/${requestId}`,{}, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};
// Save Service Form
export const saveServiceForm = async (data: any) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return apiClient.post('/dsd/request/serviceForm/save', data, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

// Get Service Form by ID
export const getServiceForm = async (serviceFormId: number) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return apiClient.get(`/dsd/request/serviceForm/${serviceFormId}`, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

export interface Location {
    locationId: number;
    customerId: number;
    displayName: string | null;
    locationType: string;
    isPrimary: 'Y' | 'N';
    addressLine1: string;
    addressLine2: string | null;
    addressLine3: string | null;
    city: string;
    state: string;
    country: string;
    postalCode: string;
}

export interface Customer {
    customerId: number;
    businessName: string;
    displayName: string;
    legalName: string;
    customerCode: string;
    status: string;
    sfOpportunityId: string;
    relationshipStatus: string;
    customerType: string;
    website: string;
    region: string;
    isGlobalAgreement: 'Y' | 'N';
    isSubsidiary: 'Y' | 'N';
    locations: Location[];
}

export const getCustomers = async (): Promise<Customer[]> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  const response = await apiClient.get('/customer', {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
    return response.data;
};

// Add a new customer
export interface AddCustomerPayload {
  businessName: string;
  displayName: string;
  legalName: string;
  customerCode: string;
  status: string;
  sfOpportunityId: string;
  relationshipStatus: string;
  customerType: string;
  website: string;
  region: string;
  isGlobalAgreement: 'Y' | 'N';
  isSubsidiary: 'Y' | 'N';
  customerId?: number;
}

export const addCustomer = async (payload: AddCustomerPayload): Promise<Customer> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  const response = await apiClient.post('/customer', payload, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
  return response.data;
};


// Delete a location
export const deleteLocation = async (locationId: number): Promise<void> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  await apiClient.patch(`/location/${locationId}`,{}, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

// Delete a customer
export const deleteCustomer = async (customerId: number): Promise<void> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  await apiClient.patch(`/customer/${customerId}`,{}, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

// Add a new location for a customer
export interface AddLocationPayload {
  customerId: number;
  displayName: string;
  locationType: string;
  isPrimary: 'Y' | 'N';
  addressLine1: string;
  addressLine2: string | null;
  addressLine3: string | null;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  locationId?: number;
}

export const addLocation = async (payload: AddLocationPayload): Promise<Location> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  const response = await apiClient.post('/location', payload, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
  return response.data;
};

export interface SalesRequestDto {
  requestId: number;
  requestNumber: string;
  businessId: number;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
  dsdRequestStatus: number;
  // Add other fields as needed
}

export const getSalesRequests = async (): Promise<SalesRequestDto[]> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  const response = await apiClient.get('/dsd/request/view', {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
  return response.data as SalesRequestDto[];
};

export const getMySalesRequests = async (): Promise<SalesRequestDto[]> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  const response = await apiClient.get('/dsd/request/view/my', {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
  return response.data as SalesRequestDto[];
};

export interface PriceDeskRequest {
  requestId: number;
  requestNumber: string;
  customerId: number;
  locationId: number | null;
  isGlobalAgreement: string | null;
  networkScan: string | null;
  customerRelationship: string | null;
  isRfp: string | null;
  isMsa: string | null;
  rfpDueDate: string | null;
  printAssessment: string | null;
  salesRepOid: string | null;
  salesManagerOid: string | null;
  regionalLeaderOid: string | null;
  salesChannel: string | null;
  salesChannelValue: string | null;
  portfolioId: number | null;
  salesBranch: string | null;
  pricingCategoryCode: string | null;
  pricingCategoryValue: string | null;
  minimumCommitmentAmount: number | null;
  foreignBuyout: string | null;
  leaseTermInMonth: number | null;
  contractType: string | null;
  billingPeriod: string | null;
  billingPeriodValue: string | null;
  supplierType: string | null;
  supplierTypeValue: string | null;
  implementation: string | null;
  implementationValue: string | null;
  proposalDate: string | null;
  paymentMode: string | null;
  paymentModeValue: string | null;
  paymentJustification: string | null;
  currency: string | null;
  potentialUnitCount: number | null;
  installationDate: string | null;
  specialConsideration: string | null;
  hardwarePricingStrategy: string | null;
  softwareSalesStrategy: string | null;
  currentIncumbent: string | null;
  dsdRequestStatus: number;
  serviceFormStatus: string | null;
  cmacRequestStatus: string | null;
  extensionRequestStatus: string | null;
  serviceFormId: number | null;
  extensionFormId: number | null;
  registrationFormId: number | null;
  createdAt: string;
  createdBy: string;
  createdByName: string;
  updatedAt: string;
  updatedBy: string;
  updatedByName: string;
  customer: Customer;
  location: any | null; // Define more specific type if needed
  portfolio: any | null; // Define more specific type if needed
  requestItems: any | null; // Define more specific type if needed
  mfpIncumbents: any | null; // Define more specific type if needed
  success: boolean | null;
  message: string | null;
  errors: any | null; // Define more specific type if needed
  contractTypeValue: string | null;
  // Indicates whether the Profit & Loss for this request has been approved ('Y' or 'N')
  isPnlApproved?: 'Y' | 'N';
}

export const getPriceDeskList = async (): Promise<PriceDeskRequest[]> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { Authorization: `Bearer ${token}` };
  const { data } = await apiClient.get<PriceDeskRequest[]>('/dsd/request/pl', {
    headers: { ...apiClient.defaults.headers.common, ...additionalHeaders }
  });
  return data;
};

export const getServiceDeskList = async (): Promise<PriceDeskRequest[]> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { Authorization: `Bearer ${token}` };
  const { data } = await apiClient.get<PriceDeskRequest[]>('/dsd/serviceFormList', {
    headers: { ...apiClient.defaults.headers.common, ...additionalHeaders }
  });
  return data;
};

// ----------------------------- CMAC Registration -----------------------------
export const getCmacRegistration = async (requestId: number) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return apiClient.get(`/dsd/request/cmac/${requestId}`, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

export interface SaveCmacRegistrationPayload {
  requestId: number;
  locationType: string | null;
  customerType: string | null;
  subsidaryInfo: string | null;
  requestedStartDate: string | null; // ISO Date string e.g. 2025-08-01T00:00:00
  requestTermInMonth: number | null;
  registrationSelection: string | null;
}

export const saveCmacRegistration = async (payload: SaveCmacRegistrationPayload) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return apiClient.post('/dsd/request/cmac/save', payload, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

// ----------------------------- CMAC Extension -----------------------------
export interface SaveCmacExtensionPayload {
  requestId: number;
  isMsa: string; // 'Y' | 'N'
  extensionInMonth: number;
  salesJustificationText: string;
  revenueTd: number;
  targetTd: number;
  noOfExtensionTd: number;
  supportAverage: string;
  priceDeskJustificationText: string;
}

export const getCmacExtension = async (requestId: number) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return apiClient.get(`/dsd/request/extension/${requestId}`, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

export const saveCmacExtension = async (
  payload: SaveCmacExtensionPayload
) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return apiClient.post('/dsd/request/extension/save', payload, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

export const getSalesRequest = async (requestId: number) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
  return apiClient.get(`/dsd/request/${requestId}`, {
    headers: {
      ...apiClient.defaults.headers.common,
      ...additionalHeaders
    }
  });
};




/**
 * Profit & Loss calculator data for a sales request
 * GET /dsd/request/pl/{requestId}
 */
export interface ProfitLossItem {
  requestItemId: number;
  itemId: number;
  dealerIt: number;
  dsdQuantity: number;
  msrp: number | null;
  requestedSellingPrice: number | null;
  parentItemId: number | null;
  itemNumber: string | null;
  displayName: string | null;
  description: string | null;
  modelName: string | null;
  isMainUnit: string | null;
  isMainframe: string | null;
  sellByDsd: string | null;
  sellByDealer: string | null;
  isSolution: string | null;
  isSolutionGroup: string | null;
  currency: string | null;
  wholesaleCost: number | null;
  requestedSellingPricePL: number | null;
  cmacPercentage: number | null;
  cmacPrice: number | null;
  customer: Customer;
}

// Profit & Loss API response shape
export interface ProfitLossResponse {
  items: ProfitLossItem[];
  customer?: Customer;
  pricingCategoryValue?: string | null;
  // Backend-provided flag to indicate P&L approval
  isPnlApproved?: 'Y' | 'N';
}

export interface ProfitLossSavePayload {
  requestItemId: number;
  itemId: number;
  requestId: number;
  requestedSellingPricePL: number;
  cmacPrice: number;
}

export const getProfitLossData = async (requestId: number): Promise<ProfitLossResponse> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { Authorization: `Bearer ${token}` };
  const {data} = await apiClient.get<ProfitLossResponse>(`/dsd/request/pl/${requestId}`, {
    headers: { ...apiClient.defaults.headers.common, ...additionalHeaders }
  });
  return data;
};

/**
 * Save modified Profit & Loss changes for given requestId.
 * POST /dsd/request/pl/{requestId}
 */
export const saveProfitLossChanges = async (
  requestId: number,
  payload: any
): Promise<void> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { Authorization: `Bearer ${token}` };
  await apiClient.post(`/dsd/request/pl/${requestId}`, payload, {
    headers: { ...apiClient.defaults.headers.common, ...additionalHeaders }
  });
};

export function toDateInputValue(isoString: string): string {
  if (!isoString) return '';
  // Handles both ISO and YYYY-MM-DD
  if (/^\d{4}-\d{2}-\d{2}$/.test(isoString)) return isoString;
  const date = new Date(isoString);
  if (isNaN(date.getTime())) return '';
  return date.toISOString().slice(0, 10);
}

/* ------------------------------------------------------------------
 * Catalog-item related helpers
 * ------------------------------------------------------------------ */
export interface CatalogItem {
  id: number;
  itemId: number;
  parentItemId: number | null;
  itemNumber: string | null;
  displayName: string;
  sellByDsd: 'Y' | 'N';
  sellByDealer: 'Y' | 'N';
  isSolution: 'Y' | 'N';
  isSolutionGroup: 'Y' | 'N';
  description: string;
  modelName: string;
  isActive: 'Y' | 'N';
  isTerminated: 'Y' | 'N';
  isMainUnit: 'Y' | 'N';
  isMainframe: 'Y' | 'N';
  itemPriceId: string | null;
  currency: string | null;
  msrp: number | null;
  wholesaleCost: number | null;
  isVpRateEnable: 'Y' | 'N';
}

/**
 * Hardware main-frame main units
 * GET /item/mainframe/mainUnit/
 */
export const getHardwareMainUnits = async (): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { Authorization: `Bearer ${token}` };
  const cacheKey = 'worksheetData';
  const cachedData = sessionStorage.getItem(cacheKey);

  if (cachedData) {
      const data = JSON.parse(cachedData);
      return Promise.resolve({
          data,
          status: 200,
          statusText: 'OK (from cache)',
          headers: {},
          config: {} as any,
      });
  }

  const { data } = await apiClient.get<CatalogItem[]>('/item/mainframe/mainUnit/', {
    headers: { ...apiClient.defaults.headers.common, ...additionalHeaders }
  });

  if (data) {
      sessionStorage.setItem(cacheKey, JSON.stringify(data));
  }

  return Promise.resolve({
      data,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
  });
};

/**
 * Accessories for a given hardware parent item
 * GET /item/mainframe/mainUnit/{parentId}
 */
export const getAccessories = async (parentItemId: number): Promise<CatalogItem[]> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { Authorization: `Bearer ${token}` };
  const { data } = await apiClient.get<CatalogItem[]>(`/item/mainframe/mainUnit/${parentItemId}`, {
    headers: { ...apiClient.defaults.headers.common, ...additionalHeaders }
  });
  return data;
};

/**
 * Solution main-unit categories
 * GET /item/solution/mainUnit/
 */
export const getSolutionMainUnits = async (): Promise<CatalogItem[]> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { Authorization: `Bearer ${token}` };
  const { data } = await apiClient.get<CatalogItem[]>('/item/solution/mainUnit/', {
    headers: { ...apiClient.defaults.headers.common, ...additionalHeaders }
  });
  return data;
};

/**
 * Sub-solution items for a given solution parent
 * GET /item/solution/mainUnit/{parentId}
 */
export const getSolutionSubItems = async (parentItemId: number): Promise<CatalogItem[]> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { Authorization: `Bearer ${token}` };
  const { data } = await apiClient.get<CatalogItem[]>(`/item/solution/mainUnit/${parentItemId}`, {
    headers: { ...apiClient.defaults.headers.common, ...additionalHeaders }
  });
  return data;
};
