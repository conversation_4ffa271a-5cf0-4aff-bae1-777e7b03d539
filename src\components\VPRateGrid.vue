<template>
  <v-container fluid>
    <v-card>
      <v-card-title class="text-h5 py-4 bg-grey-lighten-4 d-flex align-center justify-space-between">
        <span>VP Rate Table</span>
        <v-btn
          color="primary"
          variant="outlined"
          prepend-icon="download"
          :loading="exportLoading"
          @click="exportVPRates"
          size="small"
        >
          Export VP Rates
        </v-btn>
      </v-card-title>

      <!-- Filter Section -->
      <v-card-text class="pb-0">
        <v-row class="mb-4 mt-4">
          <!-- Portfolio Filter -->
          <v-col cols="12" md="3">
            <v-select
              v-model="selectedPortfolioId"
              :items="portfolioOptions"
              item-title="title"
              item-value="value"
              label="Portfolio"
              clearable
              variant="outlined"
              density="compact"
              :loading="loadingDropdowns"
              :disabled="loadingDropdowns"
              prepend-inner-icon="business"
            ></v-select>
          </v-col>

          <!-- Mainframe Autocomplete -->
          <v-col cols="12" md="3">
            <v-autocomplete
              v-model="selectedMainframeId"
              :items="mainframeOptions"
              item-title="title"
              item-value="value"
              label="Mainframe Unit"
              clearable
              variant="outlined"
              density="compact"
              :loading="loadingDropdowns"
              :disabled="loadingDropdowns"
              prepend-inner-icon="hardware"
              @update:model-value="onMainframeChange"
            >
              <template v-slot:item="{ props: itemProps, item }">
                <v-list-item
                  v-bind="itemProps"
                  :class="{
                    'vp-rate-enabled': item.raw.isVpRateEnable === 'Y',
                    'vp-rate-disabled': item.raw.isVpRateEnable === 'N'
                  }"
                ></v-list-item>
              </template>
            </v-autocomplete>
          </v-col>

          <!-- Accessory Autocomplete -->
          <v-col cols="12" md="3">
            <v-autocomplete
              v-model="selectedAccessoryId"
              :items="accessoryOptions"
              item-title="title"
              item-value="value"
              label="Accessory"
              clearable
              variant="outlined"
              density="compact"
              :loading="loadingAccessories"
              :disabled="loadingAccessories || !selectedMainframeId"
              prepend-inner-icon="extension"
              placeholder="Select Mainframe first"
            >
              <template v-slot:item="{ props: itemProps, item }">
                <v-list-item
                  v-bind="itemProps"
                  :class="{
                    'vp-rate-enabled': item.raw.isVpRateEnable === 'Y',
                    'vp-rate-disabled': item.raw.isVpRateEnable === 'N'
                  }"
                ></v-list-item>
              </template>
            </v-autocomplete>
          </v-col>

          <!-- Inline Actions -->
          <v-col cols="12" md="3" class="d-flex align-start justify-center" style="gap: 8px;">
            <v-btn
              color="primary"
              @click="searchVPRates"
              :loading="loading"
              :disabled="loading || !selectedPortfolioId || !selectedMainframeId"
              variant="elevated"
            >
              <v-icon left>search</v-icon>
              Search
            </v-btn>

            <v-btn
              color="secondary"
              @click="clearFilters"
              :disabled="loading"
              variant="outlined"
              class="mr-2"
            >
              <v-icon left>filter_list_off</v-icon>
              Clear
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>

      <v-card-text>
        <!-- Loading State -->
        <div v-if="loading" class="text-center pa-8">
          <v-progress-circular indeterminate color="primary" size="64"></v-progress-circular>
          <div class="mt-4 text-h6">Loading VP Rate data...</div>
        </div>

        <!-- Error State -->
        <v-alert v-else-if="error" type="error" class="ma-4">
          <v-alert-title>Error Loading Data</v-alert-title>
          {{ error }}
          <template v-slot:append>
            <v-btn color="white" variant="text" @click="searchVPRates">
              <v-icon>refresh</v-icon>
              Retry
            </v-btn>
          </template>
        </v-alert>

        <!-- VP Rate Grid -->
        <div v-else-if="vpRateData.length > 0">
          <div class="grid-action-bar d-flex justify-end mb-2">
            <v-btn
              color="success"
              @click="saveChanges"
              :loading="saving"
              :disabled="saving || !hasChanges || vpRateData.length === 0"
              variant="elevated"
              size="small"
            >
              <v-icon left>save</v-icon>
              Save Changes
            </v-btn>
          </div>

          <div class="vp-rate-grid">
          <!-- Grouped Header (Merged) -->
          <div class="grid-top-header">
            <div class="header-cell portfolio-header"></div>
            <div class="header-cell group-header" style="flex: 5;">{{getPortfolioName}}</div>
            <div class="header-cell group-header" style="flex: 5;">{{getPortfolioName}}-INCLUSIVE</div>
          </div>

          <!-- Grid Header -->
          <div class="grid-header">
            <div class="header-cell portfolio-header">Service Pack</div>
            <div class="header-cell service-header" v-for="service in serviceColumns" :key="service.key">
              {{ service.title }}
            </div>
          </div>

          <!-- Grid Rows -->
          <div class="grid-row" v-for="(row, rowIndex) in vpRateData" :key="row.portfolioLevel">
            <div class="row-header">{{ row.portfolioLevel }}</div>
            <div 
              class="editable-cell" 
              v-for="service in serviceColumns" 
              :key="`${row.portfolioLevel}-${service.key}`"
            >
              <v-text-field
                v-model="row[service.key]"
                variant="outlined"
                density="compact"
                hide-spin-buttons
                hide-details
                type="number"
                step="0.01"
                min="0"
                class="rate-input"
                @update:model-value="(val) => markAsChanged(rowIndex, service.key, val)"
                :class="{ 'changed': isFieldChanged(rowIndex, service.key) }"
              ></v-text-field>
            </div>
          </div>
          </div>
          </div>

        <!-- No Data State -->
        <div v-else-if="!loading" class="text-center pa-8">
          <v-icon size="64" color="grey-lighten-1">table_chart</v-icon>
          <div class="text-h6 mt-4 text-grey-darken-1">
            {{ selectedPortfolioId ? 'No VP Rate data found' : 'Select Portfolio and Item and search to view VP Rate data' }}
          </div>
        </div>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { getVPRates, getWorksheetData, getWorksheetSubProducts, saveVpRate, updateVpRate, exportVPRateAPI } from '@/lib/api';
import { getLov, LovCategories, type LovItem } from '@/services/lovService';
import { useAppStore } from '@/stores/AppStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';

const appStore = useAppStore();
const snackbarStore = useSnackbarStore();

// Reactive data
const loading = ref(false);
const saving = ref(false);
const exportLoading = ref(false);
const error = ref('');
const vpRateData = ref<any[]>([]);
const originalData = ref<any[]>([]);
const changedFields = ref<Set<string>>(new Set());

// Filter values
const selectedPortfolioId = ref<number | null>(null);
const selectedMainframeId = ref<number | null>(null);
const selectedAccessoryId = ref<number | null>(null);

// Dropdown options
const portfolioOptions = ref<{ title: string; value: number }[]>([]);
const mainframeOptions = ref<{ title: string; value: number; isVpRateEnable?: 'Y' | 'N' }[]>([]);
const accessoryOptions = ref<{ title: string; value: number; isVpRateEnable?: 'Y' | 'N' }[]>([]);

// Loading states
const loadingDropdowns = ref(false);
const loadingAccessories = ref(false);

// Service columns configuration based on the mockup
const serviceColumns = ref([
  { key: 'bw', title: 'B&W' },
  { key: 'clr', title: 'Color' },
  { key: 'os', title: 'Over Size' },
  { key: 'minBase', title: 'Min Base' },
  { key: 'minVol', title: 'Min Vol' },
  { key: 'bwInclusive', title: 'B&W' },
  { key: 'clrInclusive', title: 'Color' },
  { key: 'osInclusive', title: 'Over Size' },
  { key: 'minBaseInclusive', title: 'Min Base' },
  { key: 'minVolInclusive', title: 'Min Vol' }
]);

// Computed properties
const getPortfolioName = computed(() => {
  if (selectedPortfolioId.value) {
    const portfolio = portfolioOptions.value.find(p => p.value === selectedPortfolioId.value);
    return portfolio?.title || 'Portfolio';
  }
  return 'Portfolio';
});

const hasChanges = computed(() => {
  return changedFields.value.size > 0;
});

// Watch for mainframe selection to load accessories
watch(selectedMainframeId, (newValue) => {
  selectedAccessoryId.value = null;
  accessoryOptions.value = [];
  if (newValue) {
    loadAccessories(newValue);
  }
});

// Methods
const onMainframeChange = (value: number | null) => {
  selectedAccessoryId.value = null;
  accessoryOptions.value = [];
  // Clear grid state on mainframe change to avoid saving stale data
  vpRateData.value = [];
  originalData.value = [];
  changedFields.value.clear();
  error.value = '';
  if (value) {
    loadAccessories(value);
  }
};

// Also clear grid state when portfolio or accessory changes
watch(selectedPortfolioId, () => {
  vpRateData.value = [];
  originalData.value = [];
  changedFields.value.clear();
  error.value = '';
});

watch(selectedAccessoryId, () => {
  vpRateData.value = [];
  originalData.value = [];
  changedFields.value.clear();
  error.value = '';
});

const loadAccessories = async (mainframeId: number) => {
  try {
    loadingAccessories.value = true;
    const response = await getWorksheetSubProducts(mainframeId.toString());
    accessoryOptions.value = (response.data || []).map((item: any) => ({
      title: item.displayName || item.itemNumber || `Item ${item.itemId}`,
      value: Number(item.itemId),
      isVpRateEnable: item.isVpRateEnable
    }));
  } catch (err: any) {
    console.error('Error loading accessories:', err);
    snackbarStore.show({
      text: 'Failed to load accessories',
      color: 'error',
      icon: 'alert',
      timeout: 3000
    });
  } finally {
    loadingAccessories.value = false;
  }
};

const searchVPRates = async () => {
  if (!selectedPortfolioId.value) {
    snackbarStore.show({
      text: 'Please select a Portfolio',
      color: 'warning',
      icon: 'warning',
      timeout: 3000
    });
    return;
  }

  try {
    loading.value = true;
    error.value = '';

    // Build query parameters
    const params: any = {
      portfolioId: selectedPortfolioId.value
    };

    // Add itemId based on selection priority: accessory > mainframe
    if (selectedAccessoryId.value) {
      params.itemId = selectedAccessoryId.value;
    } else if (selectedMainframeId.value) {
      params.itemId = selectedMainframeId.value;
    }

    const response = await getVPRates(params);
    const rawData = response.data || [];

    // Transform API data to grid format
    vpRateData.value = transformDataToGrid(rawData);
    originalData.value = JSON.parse(JSON.stringify(vpRateData.value));
    changedFields.value.clear();

    if (vpRateData.value.length === 0) {
      snackbarStore.show({
        text: 'No VP Rate data found for the selected criteria',
        color: 'info',
        icon: 'info',
        timeout: 3000
      });
    }

  } catch (err: any) {
    console.error('Error fetching VP Rate data:', err);
    error.value = err.response?.data?.message || err.message || 'Failed to load VP Rate data';
    
    snackbarStore.show({
      text: 'Failed to load VP Rate data',
      color: 'error',
      icon: 'alert',
      timeout: 5000
    });
  } finally {
    loading.value = false;
  }
};

const transformDataToGrid = (rawData: any[]) => {
  // Map API rows into 4 grid rows (Basic, Gold, Diamond, Platinum)
  const portfolioLevels = ['Basic', 'Gold', 'Diamond', 'Platinum'];
  const norm = (s: any) => (s ?? '').toString().trim().toLowerCase();

  return portfolioLevels.map(level => {
    const standard = rawData.find((item: any) =>
      norm(item.servicePackValue || item.portfolioLevel) === norm(level) &&
      (item.isDealerAcceptedSr === 'N' || item.isDealerAcceptedSr === 'n' || item.isDealerAcceptedSr === false)
    ) || null;

    const inclusive = rawData.find((item: any) =>
      norm(item.servicePackValue || item.portfolioLevel) === norm(level) &&
      (item.isDealerAcceptedSr === 'Y' || item.isDealerAcceptedSr === 'y' || item.isDealerAcceptedSr === true)
    ) || null;

    return {
      portfolioLevel: level,
      servicePackId: standard?.servicePackId ?? inclusive?.servicePackId ?? null,
      vpRateIdN: standard?.vpRateId ?? null,
      vpRateIdY: inclusive?.vpRateId ?? null,
      // Standard (isDealerAcceptedSr = 'N')
      bw: standard?.blackAndWhite ?? null,
      clr: standard?.colorValue ?? null,
      os: standard?.oversizedValue ?? null,
      minBase: (standard?.minimumBase ?? standard?.minBase ?? standard?.minimumBaseAmount) ?? null,
      minVol: (standard?.minimumVolume ?? standard?.minVol ?? standard?.minimumBaseVolume) ?? null,
      // Inclusive (isDealerAcceptedSr = 'Y')
      bwInclusive: inclusive?.blackAndWhite ?? null,
      clrInclusive: inclusive?.colorValue ?? null,
      osInclusive: inclusive?.oversizedValue ?? null,
      minBaseInclusive: (inclusive?.minimumBase ?? inclusive?.minBase ?? inclusive?.minimumBaseAmount) ?? null,
      minVolInclusive: (inclusive?.minimumVolume ?? inclusive?.minVol ?? inclusive?.minimumBaseVolume) ?? null
    };
  });
};

const markAsChanged = (rowIndex: number, fieldKey: string, newVal: any) => {
  const fieldId = `${rowIndex}-${fieldKey}`;
  const originalRow = originalData.value?.[rowIndex] ?? {};
  const originalVal = originalRow[fieldKey];

  const isEmpty = (v: any) => v === '' || v === null || v === undefined;
  if (isEmpty(originalVal) && isEmpty(newVal)) {
    changedFields.value.delete(fieldId);
    return;
  }

  const toNum = (v: any) => (isEmpty(v) ? NaN : Number(v));
  const origNum = toNum(originalVal);
  const newNum = toNum(newVal);

  const isSame = !isNaN(origNum) && !isNaN(newNum)
    ? origNum === newNum
    : originalVal === newVal;

  if (!isSame) {
    changedFields.value.add(fieldId);
  } else {
    changedFields.value.delete(fieldId);
  }
};

const isFieldChanged = (rowIndex: number, fieldKey: string) => {
  const fieldId = `${rowIndex}-${fieldKey}`;
  return changedFields.value.has(fieldId);
};

const saveChanges = async () => {
  try {
    saving.value = true;
    // Build create/update payloads and filter out entries with all-null numeric values
    const createPayload: any[] = [];
    const updatePayload: any[] = [];
    const toNumberOrNull = (v: any) => {
      if (v === '' || v === null || v === undefined) return null;
      const n = Number(v);
      return Number.isFinite(n) ? n : null;
    };

    for (const row of vpRateData.value) {
      const base = {
        portfolioId: selectedPortfolioId.value,
        itemId: selectedAccessoryId.value || selectedMainframeId.value,
        servicePackId: row.servicePackId ?? null,
        isActive: 'Y',
      } as any;

      const entries = [
        {
          ...base,
          vpRateId: Number(row.vpRateIdN) || null,
          isDealerAcceptedSr: 'N',
          blackAndWhite: toNumberOrNull(row.bw),
          colorValue: toNumberOrNull(row.clr),
          oversizedValue: toNumberOrNull(row.os),
          minimumBase: toNumberOrNull(row.minBase),
          minimumVolume: toNumberOrNull(row.minVol)
        },
        {
          ...base,
          vpRateId: Number(row.vpRateIdY) || null,
          isDealerAcceptedSr: 'Y',
          blackAndWhite: toNumberOrNull(row.bwInclusive),
          colorValue: toNumberOrNull(row.clrInclusive),
          oversizedValue: toNumberOrNull(row.osInclusive),
          minimumBase: toNumberOrNull(row.minBaseInclusive),
          minimumVolume: toNumberOrNull(row.minVolInclusive)
        }
      ];

      for (const e of entries) {
        const hasAnyRate = [e.blackAndWhite, e.colorValue, e.oversizedValue, e.minimumBase, e.minimumVolume]
          .some(v => v !== null && v !== undefined);
        if (!hasAnyRate) continue;

        const idNum = typeof e.vpRateId === 'number' ? e.vpRateId : Number(e.vpRateId);
        if (idNum && idNum > 0) {
          updatePayload.push(e);
        } else {
          e.vpRateId = null; // ensure null for new entries
          createPayload.push(e);
        }
      }
    }

    if (createPayload.length === 0 && updatePayload.length === 0) {
      snackbarStore.show({
        text: 'Nothing to save. No non-empty rates found.',
        color: 'info',
        icon: 'info',
        timeout: 3000
      });
      return;
    }

    const ops: Array<{ name: 'update' | 'save'; promise: Promise<any> }> = [];
    if (updatePayload.length) ops.push({ name: 'update', promise: updateVpRate(updatePayload) });
    if (createPayload.length) ops.push({ name: 'save', promise: saveVpRate(createPayload) });

    const results = await Promise.allSettled(ops.map(o => o.promise));
    const failures = results
      .map((r, i) => ({ r, name: ops[i].name }))
      .filter(x => (x.r as PromiseSettledResult<any>).status === 'rejected');

    if (failures.length > 0) {
      const firstErr: any = (failures[0].r as any).reason;
      console.error('Error saving/updating VP Rate data:', firstErr);
      snackbarStore.show({
        text: firstErr?.response?.data?.message || 'Failed to save some VP Rate data',
        color: 'error',
        icon: 'alert',
        timeout: 5000
      });
      return;
    }

    snackbarStore.show({
      text: 'VP Rate data saved successfully',
      color: 'success',
      icon: 'check',
      timeout: 3000
    });

    // Reset change tracking
    originalData.value = JSON.parse(JSON.stringify(vpRateData.value));
    changedFields.value.clear();

  } catch (err: any) {
    console.error('Error saving VP Rate data:', err);
    snackbarStore.show({
      text: err.response?.data?.message || 'Failed to save VP Rate data',
      color: 'error',
      icon: 'alert',
      timeout: 5000
    });
  } finally {
    saving.value = false;
  }
};

const clearFilters = () => {
  selectedPortfolioId.value = null;
  selectedMainframeId.value = null;
  selectedAccessoryId.value = null;
  accessoryOptions.value = [];
  vpRateData.value = [];
  originalData.value = [];
  changedFields.value.clear();
  error.value = '';
};

// Export VP Rates
const exportVPRates = async () => {
  try {
    exportLoading.value = true;
    const response = await exportVPRateAPI();

    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');

    let filename = 'vp_rates_export.xlsx';
    const contentDisposition = (response.headers as any)['content-disposition'];
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    snackbarStore.show({
      text: 'VP Rates exported successfully!',
      color: 'success',
      icon: 'download',
      timeout: 2000
    });
  } catch (error) {
    console.error('Error exporting VP Rates:', error);
    snackbarStore.show({
      text: 'Failed to export VP Rates.',
      color: 'error',
      icon: 'alert',
      timeout: 2000
    });
  } finally {
    exportLoading.value = false;
  }
};

// Load dropdown options
const loadDropdownOptions = async () => {
  try {
    loadingDropdowns.value = true;

    // Load all dropdown options concurrently
    const [portfolioLovs, mainframeResponse] = await Promise.all([
      getLov(LovCategories.PORTFOLIO),
      getWorksheetData()
    ]);

    // Map portfolio options
    portfolioOptions.value = portfolioLovs.map((item: LovItem) => ({
      title: item.description,
      value: Number(item.lookupCode)
    }));

    // Map mainframe unit options
    mainframeOptions.value = (mainframeResponse.data || []).map((item: any) => ({
      title: item.displayName || item.itemNumber || `Item ${item.itemId}`,
      value: Number(item.itemId),
      isVpRateEnable: item.isVpRateEnable
    }));

  } catch (err: any) {
    console.error('Error loading dropdown options:', err);
    snackbarStore.show({
      text: 'Failed to load filter options',
      color: 'error',
      icon: 'alert',
      timeout: 5000
    });
  } finally {
    loadingDropdowns.value = false;
  }
};

// Lifecycle
onMounted(async () => {
  await loadDropdownOptions();
  appStore.stopPageLoader();
});
</script>

<style scoped>
.vp-rate-grid {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.grid-header {
  display: flex;
  background: #f5f5f5;
  border-bottom: 2px solid #ddd;
  font-weight: 600;
}

.grid-top-header {
  display: flex;
  background: #eef3fd;
  border-bottom: 1px solid #ddd;
  font-weight: 700;
}

.header-cell {
  padding: 12px 8px;
  border-right: 1px solid #ddd;
  text-align: center;
  font-size: 0.875rem;
}

.portfolio-header {
  min-width: 120px;
  background: #e3f2fd;
  font-weight: 700;
}

.service-header {
  min-width: 100px;
  flex: 1;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-row {
  display: flex;
  border-bottom: 1px solid #eee;
}

.grid-row:hover {
  background: #fafafa;
}

.row-header {
  min-width: 120px;
  padding: 8px 12px;
  border-right: 1px solid #ddd;
  background: #f8f9fa;
  font-weight: 600;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.editable-cell {
  min-width: 100px;
  flex: 1;
  padding: 4px;
  border-right: 1px solid #eee;
}

.rate-input {
  font-size: 0.875rem;
}

.rate-input.changed :deep(.v-field) {
  background-color: #fff3cd;
  border-color: #ffc107;
}

/* Responsive design */
@media (max-width: 768px) {
  .vp-rate-grid {
    overflow-x: auto;
  }
  
  .grid-header,
  .grid-row {
    min-width: 800px;
  }
}

/* Custom scrollbar for horizontal scroll */
.vp-rate-grid::-webkit-scrollbar {
  height: 8px;
}

.vp-rate-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.vp-rate-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.vp-rate-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* VP Rate background colors for autocomplete items */
.vp-rate-enabled {
  background-color: #e8f5e8 !important; /* Light green */
}

.vp-rate-disabled {
  background-color: #ffebee !important; /* Light red */
}

.vp-rate-enabled:hover {
  background-color: #c8e6c9 !important; /* Darker green on hover */
}

.vp-rate-disabled:hover {
  background-color: #ffcdd2 !important; /* Darker red on hover */
}
</style>
