<script setup lang="ts">
import { ref, reactive, defineProps, onMounted, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import ServiceTonerPack from '@/components/service/ServiceTonerPack.vue';
import ServiceApprovalsLease from '@/components/service/ServiceApprovalsLease.vue';
import ServiceProductDetails from '@/components/service/ServiceProductDetails.vue';
import ServiceAccessoryList from '@/components/service/ServiceAccessoryList.vue';
import ServiceCompetitiveInfo from '@/components/service/ServiceCompetitiveInfo.vue';
import ServiceCurrentEquipment from '@/components/service/ServiceCurrentEquipment.vue';
import ServiceBusinessCase from '@/components/service/ServiceBusinessCase.vue';
import * as XLSX from 'xlsx';
import { getHardwareChargesRates } from '@/lib/api';
import { getHardwareMainUnits } from '@/services/salesRequestService';
import { saveServiceForm , getServiceForm } from '@/services/salesRequestService';

const { t } = useI18n();

const props = defineProps({
  // Summary of MSRP from WorksheetsTab { amount:number, percentage:number }
  msrpSummary: {
    type: Object as () => { amount: number; percentage: number },
    required: false,
    default: () => ({ amount: 0, percentage: 0 }),
  },
  // Payment information from PaymentDetailsTab (whatever getFormData returns)
  paymentInfo: {
    type: Object as () => Record<string, any>,
    required: false,
    default: () => ({}),
  },
  // Raw worksheet items array so the tab can derive main hardware information internally
  worksheetItems: {
    type: Array as () => any[],
    required: false,
    default: () => [],
  },
  // Selected portfolioId from CustomerDetailsTab
  portfolioId: {
    type: Number,
    required: false,
    default: null,
  },
  // The current Sales Request ID to associate with this Service Form
  requestId: {
    type: Number,
    required: false,
    default: null,
  },
  // Existing Service Form ID (for edit mode)
  serviceFormId: {
    type: Number,
    required: false,
    default: null,
  },

  // Whether the Service tab is currently the active tab
  isActive: {
    type: Boolean,
    required: false,
    default: false,
  },
});

// Initialize formData (copied from original page)
const formData = reactive({
  service_request_form: {
    request_date: null,
    sales_representative_name: '',
    sales_manager_name: '',
    customer_business_name: '',
    customer_legal_name: '',
    address1: '',
    address2_3: '',
    city_province_postal: '',
  },
    toner_and_service_value_pack: {
    toner_in_out: null,
    service_value_pack: '',
    default_term_length: 12,
  },
  approvals_details: {
    selected_region: null,
    dsd_territory: false,
    dealer_territory: false,
    dealer_accepted_service_rates: null,
    purchase_lease_type: 'Lease',
    length_of_lease_months: null,
    msrp_details: { percentage: null, amount: null },
  },
  product_service_details: {
    models_overview: [],
    accessories_included: [],
  },
  competitive_current_usage_info: {
    details: '',
  },
  current_equipment_details: {
    equipment_list: '',
  },
  service_business_case: {
    justification: '',
  },
});

const termOptions = [
  { title: '12 Months', value: 12 },
  { title: '24 Months', value: 24 },
  { title: '36 Months', value: 36 },
  { title: '48 Months', value: 48 },
  { title: '60 Months', value: 60 },
];

// ---------------- Hardware Charges Fetch ----------------
const hardwareCharges = ref<Record<number, any>>({});

const fetchHardwareCharges = async () => {
  const servicePackIdRaw = formData.toner_and_service_value_pack.service_value_pack;
  const servicePackId = servicePackIdRaw ? Number(servicePackIdRaw) : null;
  const portfolioId = props.portfolioId ? Number(props.portfolioId) : null;
  const ids = mainHardwareInfo.value.mainUnitIds;
  if (!servicePackId || !portfolioId || ids.length === 0) return;

  const chargesMap: Record<number, any> = {};
  const existingModelsGlobal: any[] = formData.product_service_details.models_overview || [];

  await Promise.all(
    ids.map(async (itemId: number) => {
      try {
        // Pass existing fees_included_in_cpc flag (if we know it) as isDealerAcceptedSr parameter
        const existing = existingModelsGlobal.find((m) => m.id === itemId);
        const isDealerAcceptedSr = existing?.fees_included_in_cpc ? 'Y' : 'N';
        const { data } = await getHardwareChargesRates(itemId, servicePackId, portfolioId, isDealerAcceptedSr);
        chargesMap[itemId] = data;
      } catch (e) {
        console.error('Failed to fetch hardware charges for', itemId, e);
      }
    })
  );
  hardwareCharges.value = chargesMap;
  console.log('Hardware charges fetched:',mainHardwareInfo);

  // Map fetched charges into models_overview structure expected by ServiceProductDetails
  const qtyMap = mainHardwareInfo.value.quantities;
  const existingModels: any[] = formData.product_service_details.models_overview || [];

  const mappedProducts = Object.values(chargesMap).map((c: any) => {
    // If we already loaded an existing service form, preserve its extra fields
    const existing = existingModelsGlobal.find((m) => m.id === c.itemId) || {};

    return {
      // Hardware-rate fields (always replace with latest)
      ...existing,
      id: c.itemId,
      model: c.displayName,
      blackAndWhite: c.blackAndWhite,
      colorValue: c.colorValue,
      iprc: c.oversizedValue,
      minimumBase: c.minimumBase,
      minimumVolume: c.minimumVolume,

      // Quantities (prefer latest from qtyMap, fallback to existing)
      dsd_qty: qtyMap[c.itemId]?.dsd ?? existing.dsd_qty ?? null,
      dealer_qty: qtyMap[c.itemId]?.dealer ?? existing.dealer_qty ?? null,
      parentModelName: mainHardwareInfo?.value?.parentInfo?.find((p: any) => p.itemId === c.itemId)?.modelName ?? null,

      // The following fields are preserved if they already exist, otherwise default
      estimated_amv_unit: existing.estimated_amv_unit ?? null,
      colour_percentage: existing.colour_percentage ?? null,
      oversize_percentage: existing.oversize_percentage ?? null,
      fees_included_in_cpc: existing.fees_included_in_cpc ?? false,
      discounts: existing.discounts || { bw: null, colour: null, minimum_base_amt: null, minimum_base_volume: null },
      serviceOptions: existing.serviceOptions || [],
    };
  });
  console.log(mappedProducts)
  formData.product_service_details.models_overview = mappedProducts;

  // ---------------- Watch for Fees Included in CPC changes ----------------
  // We set up a watcher *after* the first mapping to ensure old state exists for comparison
  // This watcher triggers only once (lazy init) to avoid duplicate registrations
  if (!watcherInitialized.value) {
    initFeesWatch();
    watcherInitialized.value = true;
  }
};

// Utility to compare two arrays for set equality (ignoring order)
function areArraysEqual(a: any[], b: any[]) {
  if (a.length !== b.length) return false;
  const sortedA = [...a].sort();
  const sortedB = [...b].sort();
  return sortedA.every((val, idx) => val === sortedB[idx]);
}

// ---------------- Utility to refresh a single item's charges ----------------
const refreshChargesForItem = async (model: any) => {
  const servicePackIdRaw = formData.toner_and_service_value_pack.service_value_pack;
  const servicePackId = servicePackIdRaw ? Number(servicePackIdRaw) : null;
  const portfolioId = props.portfolioId ? Number(props.portfolioId) : null;
  if (!servicePackId || !portfolioId || !model?.id) return;
  try {
    const { data } = await getHardwareChargesRates(
      model.id,
      servicePackId,
      portfolioId,
      model.fees_included_in_cpc ? 'Y' : 'N'
    );
    // Update rate fields for that model
    model.blackAndWhite = data.blackAndWhite;
    model.colorValue = data.colorValue;
    model.iprc = data.oversizedValue;
    model.minimumBase = data.minimumBase;
    model.minimumVolume = data.minimumVolume;
  } catch (e) {
    console.error('Failed to refresh charges after Fees Included in CPC toggled', e);
  }
};

// ---------------- Function to create deep watcher for fees_included_in_cpc ----------------
function initFeesWatch() {
  watch(
    () => formData.product_service_details.models_overview.map((m: any) => m.fees_included_in_cpc),
    (newVals, oldVals) => {
      newVals.forEach((val: boolean, idx: number) => {
        if (oldVals && val !== oldVals[idx]) {
          const model = formData.product_service_details.models_overview[idx];
          refreshChargesForItem(model);
        }
      });
    },
    { deep: true }
  );
}

// ---------------- Main Unit derivation ----------------
const allowedMainIds = ref<number[]>([] as number[]);

const fetchAllowedMainIds = async () => {
  try {
    const all = await getHardwareMainUnits();
    allowedMainIds.value = (all.data || [])
      .filter((it: any) => it?.isVpRateEnable === 'Y')
      .map((it: any) => it.itemId);
  } catch (e) {
    console.error('Failed to fetch hardware main units', e);
  }
};

// Flag to ensure we only register the deep watcher once
const watcherInitialized = ref(false);

const mainHardwareInfo = computed(() => {
  const ids: number[] = [];
  const qty: Record<number, { dsd: number | null; dealer: number | null }> = {};
  if (!props.isActive) return { mainUnitIds: ids, quantities: qty };

  const worksheetItems = props.worksheetItems || [];
  const filteredItems = worksheetItems.filter((it: any) => it.isVpRateEnable=='Y');
  // const mainUnitItems = filteredItems.filter((it: any) => allowedMainIds.value.includes(it.itemId));
  const mainUnitItems = filteredItems;
  mainUnitItems.forEach((it: any) => {
    const id = Number(it.itemId);
    ids.push(id);
    qty[id] = { dsd: it.dsdQuantity ?? null, dealer: it.dealerIt ?? null };
  });
  return { mainUnitIds: ids, quantities: qty, parentInfo: filteredItems.map((it: any) => {
    return {
      parentItemId: it.parentItemId,
      itemId: it.itemId,
      modelName: it.parentItemId ? it.modelName : null
    };
  }) };
});



let lastIds: number[] = [];

watch(
  () => mainHardwareInfo.value.mainUnitIds,
  (newIds) => {
    if (!props.isActive) return;
    if (!areArraysEqual(newIds, lastIds)) {
      fetchHardwareCharges();
      lastIds = [...newIds];
    }
  }
);
watch(
  () => [
    props.portfolioId,
    formData.toner_and_service_value_pack.service_value_pack,
  ],
  () => {
    if (props.isActive) fetchHardwareCharges();
  },
  { deep: true }
);
// Watch for serviceFormId changes
watch(() => props.serviceFormId, loadExistingServiceForm);

// Trigger fetch when tab becomes active
watch(
  () => props.isActive,
  async (active) => {
    if (active) {
      // refresh allowed IDs then fetch charges
     // await fetchAllowedMainIds();
      fetchHardwareCharges();
    }
  },
  { immediate: true }
);

const init = async () => {
  //await fetchAllowedMainIds();
  await loadExistingServiceForm();
  if (props.isActive) {
    await fetchHardwareCharges();
  }
};

onMounted(init);

// Removed obsolete debug mount log that referenced non-existent prop 'id'

async function loadExistingServiceForm() {
  if (!props.serviceFormId) return;
  try {
    const { data } = await getServiceForm(props.serviceFormId);
    // Map top-level fields
    formData.toner_and_service_value_pack.service_value_pack = String(data.servicePackId) ?? null;
    formData.toner_and_service_value_pack.toner_in_out = data.tonerType ?? null;
    formData.approvals_details.selected_region = data.region ?? null;
    formData.approvals_details.territory = data.territory ?? null;
    formData.approvals_details.dealer_accepted_service_rates = data.isDealerAcceptedSr ?? null;
    formData.approvals_details.purchase_lease_type = data.paymentModeValue ?? null;
    formData.approvals_details.length_of_lease_months = data.leaseTermInMonth ?? null;
    formData.approvals_details.msrp_details = { amount: data.msrp, percentage: data.msrpPercent };
    formData.competitive_current_usage_info.details = data.currentUsageInformation ?? '';
    formData.current_equipment_details.equipment_list = data.currentEquipmentInfo ?? '';
    formData.service_business_case.justification = data.serviceBusinessCase ?? '';

    // Map accessories included (MFP pricing) to Accessory[] expected by ServiceAccessoryList
    const accessories = (data.dsdRequestMfpPricing || []).map((acc: any) => ({
      id: acc?.mfpPricingId ?? null,
      mfpModelName: acc?.mfpModel ?? '',
      accessoryName: acc?.accessoryIncluded ?? '',
      publishedRate: acc?.publishedRate ?? null,
      requestedRate: acc?.requestedRate ?? null,
    }));
    formData.product_service_details.accessories_included = accessories;

    // Build models_overview from serviceApprovals
    const grouped: Record<number, any> = {};
    (data.serviceApprovals || []).forEach((sa: any) => {
      if (!grouped[sa.itemId]) {
        grouped[sa.itemId] = {
          id: sa.itemId,
          model: sa.displayName,
          // blackAndWhite: sa.blackAndWhite ?? null,
          // colorValue: sa.colour ?? null,
          // iprc: sa.iprc ?? null,
          // minimumBase: sa.minimumBaseAmount ?? null,
          // minimumVolume: sa.minimumBaseVolume ?? null,
          dsd_qty: sa.dsdQuantity ?? null,
          dealer_qty: sa.dealerQuantity ?? null,
          estimated_amv_unit: sa.amvUnit ?? null,
          colour_percentage: sa.colourPercentage ?? null,
          oversize_percentage: sa.oversizePercentage ?? null,
          serviceApprovalId: sa.serviceApprovalId ?? null,
          fees_included_in_cpc: sa.cpcIncludesAccessory === 'Y',
          discounts: { bw: null, colour: null, iprc: null, minimum_base_amt: null, minimum_base_volume: null },
          serviceOptions: [],
        };
      }
      grouped[sa.itemId].serviceOptions.push({
        id: `opt_${Math.random()}`,
        serviceApprovalId: sa.serviceApprovalId ?? null,
        fixed_service_term_length: sa.fixedTermInMonth ?? null,
        bw: sa.blackAndWhite ?? null,
        colour: sa.colour ?? null,
        iprc: sa.iprc ?? null,
        minimum_base_amt: sa.minimumBaseAmount ?? null,
        minimum_base_volume: sa.minimumBaseVolume ?? null,
        isDealerAcceptedSr: (sa.isDealerAcceptedSr === true || sa.isDealerAcceptedSr === 'Y')
      });
    });
    formData.product_service_details.models_overview = Object.values(grouped);
  } catch (e) {
    console.error('Failed to load service form', e);
  }
}

const handleFormSubmit = async () => {
  // Construct payload according to required backend structure
  const payload: Record<string, any> = {
    serviceFormId: props.serviceFormId ?? null,
    requestId: props.requestId ?? null, // Expecting parent to pass requestId prop
    servicePackId: formData.toner_and_service_value_pack.service_value_pack
      ? Number(formData.toner_and_service_value_pack.service_value_pack)
      : null,
    currentUsageInformation: formData.competitive_current_usage_info.details || null,
    currentEquipmentInfo: formData.current_equipment_details.equipment_list || null,
    serviceBusinessCase: formData.service_business_case.justification || null,
    tonerType: formData.toner_and_service_value_pack.toner_in_out || null,
    region: formData.approvals_details.selected_region || null,
    territory: formData.approvals_details.territory || null,

    // Build service approvals by iterating over each model and its service options
    serviceApprovals: [] as any[],

    // Build MFP pricing list
    dsdRequestMfpPricing: [] as any[],
  };

  // ---- Service Approvals ----
  (formData.product_service_details.models_overview || []).forEach((model: any) => {
    const baseApprovalData = {
      serviceApprovalId: model.serviceApprovalId ?? null,
      serviceFormId: props.serviceFormId ?? null,
      itemId: model.id,
      dsdQuantity: model.dsd_qty ?? null,
      dealerQuantity: model.dealer_qty ?? null,
      minimumBaseAmount: model.minimumBase ?? null,
      minimumBaseVolume: model.minimumVolume ?? null,
      userRemark: 'Static/Placeholder Message For now',
      amvUnit: model.estimated_amv_unit ?? null,
      colourPercentage: model.colour_percentage ?? null,
      oversizePercentage: model.oversize_percentage ?? null,
      cpcIncludesAccessory: model.fees_included_in_cpc ? 'Y' : 'N',
    };

    // If serviceOptions exist, create approval rows per option, otherwise create one generic row.
    if (Array.isArray(model.serviceOptions) && model.serviceOptions.length) {
      model.serviceOptions.forEach((opt: any) => {
        payload.serviceApprovals.push({
          ...baseApprovalData,
          serviceApprovalId: opt.serviceApprovalId ?? null,
          fixedTermInMonth: opt.fixed_service_term_length ?? null,
          blackAndWhite: opt.bw ?? null,
          colour: opt.colour ?? null,
          iprc: opt.iprc ?? null,
          minimumBaseAmount: opt.minimum_base_amt ?? model.minimumBase ?? null,
          minimumBaseVolume: opt.minimum_base_volume ?? model.minimumVolume ?? null,
          isDealerAcceptedSr: opt.isDealerAcceptedSr ? 'Y' : 'N',
        });
      });
    } else {
      // Fallback single row
      payload.serviceApprovals.push({
        ...baseApprovalData,
        serviceApprovalId: model.serviceApprovalId ?? null,
        fixedTermInMonth: 36,
        blackAndWhite: null,
        colour: null,
        iprc: null,
        minimumBaseAmount: null,
        minimumBaseVolume: null,
        isDealerAcceptedSr:
          formData.approvals_details.dealer_accepted_service_rates === 'Y' ? 'Y' : 'N',
      });
    }
  });

  // ---- MFP Pricing ----
  const accessories: any[] = formData.product_service_details.accessories_included || [];
  payload.dsdRequestMfpPricing = accessories.map((acc: any) => ({
    mfpPricingId: acc?.id ?? null,
    mfpModel: acc?.mfpModelName ?? null,
    accessoryIncluded: acc?.accessoryName ?? null,
    publishedRate: acc?.publishedRate ?? null,
    requestedRate: acc?.requestedRate ?? null,
  }));

  console.log('Constructed Service Form payload:', payload);
  try {
    const { data } = await saveServiceForm(payload);
    console.log('Service Form saved successfully:', data);
    // TODO: Emit event or show notification as needed
  } catch (error) {
    console.error('Failed to save Service Form:', error);
    // TODO: Show error notification to user
  }
};

const cancelForm = () => {
  console.log('Service Form cancelled');
};

const exportData = () => {
  const wb = XLSX.utils.book_new();
  // Only very basic export for now
  const summaryWs = XLSX.utils.json_to_sheet([{ form: 'service', data: formData }]);
  XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');
  XLSX.writeFile(wb, 'Service-Request-Export.xlsx');
};

// ---- Sync external summaries into local form data ----
const syncMsrp = () => {
  formData.approvals_details.msrp_details.amount = props.msrpSummary.amount ?? 0;
  formData.approvals_details.msrp_details.percentage = props.msrpSummary.percentage ?? 0;
};
const syncPayment = () => {
  formData.approvals_details.purchase_lease_type = props.paymentInfo.paymentMode ?? '';
  formData.approvals_details.length_of_lease_months = props.paymentInfo.leaseTermInMonth ?? null;
};

// ---------------- Form Validation ----------------
/**
 * Validate that if any service option for a given product has colour or iprc
 * values entered, the same product must also have its colour_percentage and
 * oversize_percentage filled in. Returns true when the form passes the check.
 */
const validateForm = (): boolean => {
  const models: any[] = (formData.product_service_details.models_overview as any[]) || [];
  for (const model of models) {
    const serviceOpts: any[] = Array.isArray((model as any).serviceOptions) ? (model as any).serviceOptions : [];

    // Determine if ANY option has either colour or iprc entered
    const hasRateEntered = serviceOpts.some((opt: any) => {
      const colourFilled = opt.colour !== null && opt.colour !== undefined && opt.colour !== '';
      const iprcFilled = opt.iprc !== null && opt.iprc !== undefined && opt.iprc !== '';
      return colourFilled || iprcFilled;
    });

    if (hasRateEntered) {
      const colourPercFilled = (model as any).colour_percentage !== null && (model as any).colour_percentage !== undefined && (model as any).colour_percentage !== '';
      const iprcPercFilled = (model as any).oversize_percentage !== null && (model as any).oversize_percentage !== undefined && (model as any).oversize_percentage !== '';
      if (!colourPercFilled || !iprcPercFilled) {
        return false; // Validation failed for this model
      }
    }
  }
  return true; // All good
};

// Initial sync
syncMsrp();
syncPayment();

// Watch for changes
watch(() => props.msrpSummary, syncMsrp, { deep: true });
watch(() => props.paymentInfo, syncPayment, { deep: true });

defineExpose({
  getFormData: () => formData,
  submitServiceForm: handleFormSubmit,
  validateForm
});
</script>

<template>
  <v-container fluid>
    <div v-if="!portfolioId" class="pa-4">
      <v-alert type="error" prominent border="start">
        {{ t('page.service_request_form.errors.no_portfolio_selected', 'Please Select the Portfolio option from Customer Details Tab to proceed with the Service Form') }}
      </v-alert>
    </div>
    <v-form v-else @submit.prevent="handleFormSubmit">
      <v-row>
        <v-col cols="12">
          <h1>{{ t('page.service_request_form.title', 'Service Form') }}</h1>
        </v-col>
      </v-row>

      <!-- Toner and Service Value Pack -->
            <ServiceTonerPack 
        v-model="formData.toner_and_service_value_pack" 
        :term-options="termOptions"
      />

      <!-- Approvals and Lease Details -->
      <ServiceApprovalsLease v-model="formData.approvals_details" />

      <!-- Product/Service Details -->
            <ServiceProductDetails 
        v-model="formData.product_service_details.models_overview"
        :selected-service-value-pack="formData.toner_and_service_value_pack.service_value_pack"
        :default-term-length="formData.toner_and_service_value_pack.default_term_length"
      />

      <!-- Accessories Included -->
      <ServiceAccessoryList v-model="formData.product_service_details.accessories_included" />

      <!-- Competitive & Current Usage Information -->
      <ServiceCompetitiveInfo v-model="formData.competitive_current_usage_info.details" />

      <!-- Current Equipment Details -->
      <!-- <ServiceCurrentEquipment v-model="formData.current_equipment_details.equipment_list" /> -->

      <!-- Service Business Case -->
      <ServiceBusinessCase v-model="formData.service_business_case.justification" />

      <!-- <v-row>
        <v-col cols="12" class="d-flex justify-end mt-4">
          <v-btn color="secondary" prepend-icon="exit_to_app" variant="outlined" @click="exportData" class="mr-2">
            Export
          </v-btn>
          <v-btn color="primary" type="submit">
            Submit
          </v-btn>
        </v-col>
      </v-row> -->
    </v-form>
  </v-container>
</template>

<style scoped>
.justify-end {
}
</style>
