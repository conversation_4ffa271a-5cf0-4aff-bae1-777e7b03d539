<template>
  <v-container>
    <v-tabs v-model="activeTab" grow class="mb-4 canon-tabs" bg-color="canon">
      <v-tab value="form">CMAC Extension</v-tab>
      <v-tab value="preview" :disabled="!requestId">Preview</v-tab>
    </v-tabs>
    <v-window v-model="activeTab" class="pnl-window">
      <v-window-item value="form">
    <v-card class="mx-auto" max-width="1000">
      <v-card-title class="headline text-center">
        CMAC Extension Request Form
      </v-card-title>
      <v-card-text>
        <v-form ref="form" v-model="valid">
          <!-- Customer Information Section -->
          <v-row>
            <v-col cols="4" class="font-weight-bold">Customer Name</v-col>
            <v-col cols="8"><v-text-field v-model="formData.customerName" dense outlined></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">CMAC Number</v-col>
            <v-col cols="8"><v-text-field v-model="formData.cmacNumber" dense outlined></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">MSA (YES/NO)</v-col>
            <v-col cols="8">
              <v-select v-model="formData.msa" :items="['YES', 'NO']" dense outlined></v-select>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Length of Extension</v-col>
            <v-col cols="8"><v-text-field v-model="formData.extensionLength" dense outlined></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Models and Quantities</v-col>
            <v-col cols="8"><v-textarea :maxlength="500" v-model="formData.modelsAndQuantities" dense outlined rows="3"></v-textarea></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Size of Opportunity ($MSRP)</v-col>
            <v-col cols="8"><v-text-field v-model="formData.opportunitySize" dense outlined prefix="$"></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Extension Justification</v-col>
            <v-col cols="8"><v-textarea :maxlength="500" v-model="formData.extensionJustification" dense outlined rows="5"></v-textarea></v-col>
          </v-row>

          <!-- Price Desk Use ONLY Section -->
          <v-divider class="my-6"></v-divider>
          <h2 class="subtitle-1 font-weight-bold black white--text pa-2">Price Desk Use ONLY</h2>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Revenue TD</v-col>
            <v-col cols="8"><v-text-field v-model="priceDesk.revenueTd" dense outlined readonly></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Target TD</v-col>
            <v-col cols="8"><v-text-field v-model="priceDesk.targetTd" dense outlined readonly></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Revenue vs. Target</v-col>
            <v-col cols="8"><v-text-field v-model="revenueVsTarget" dense outlined readonly></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Number of Extensions TD</v-col>
            <v-col cols="8"><v-text-field v-model="priceDesk.numberOfExtensions" dense outlined readonly></v-text-field></v-col>
          </v-row>
           <v-row>
            <v-col cols="4" class="font-weight-bold">Support Average</v-col>
            <v-col cols="8"><v-text-field v-model="priceDesk.supportAverage" dense outlined readonly></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">PD Justification</v-col>
            <v-col cols="8"><v-textarea :maxlength="500" v-model="priceDesk.pdJustification" dense outlined rows="5" readonly></v-textarea></v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="primary" :loading="saving" :disabled="saving || loading" @click="saveExtension">
          Save
        </v-btn>
      </v-card-actions>
    </v-card>
      </v-window-item>
      <v-window-item value="preview">
        <PreviewTab v-if="requestId" :request-id="requestId" />
        <v-alert v-else type="info" variant="tonal">Request not available for preview.</v-alert>
      </v-window-item>
    </v-window>
    <v-snackbar v-model="snackbar.show" :color="snackbar.color" timeout="3000" location="top right">
      {{ snackbar.message }}
    </v-snackbar>
  </v-container>
</template>

<script>
import PreviewTab from '@/components/sales/PreviewTab.vue';
import { getCmacExtension, saveCmacExtension } from '@/services/salesRequestService';
export default {
  name: 'CmacExtensionRequestForm',
  components: { PreviewTab },
  data() {
    return {
      valid: true,
      loading: false,
      saving: false,
      activeTab: 'form',
      formData: {
        customerName: '',
        cmacNumber: '',
        msa: 'NO',
        extensionLength: '',
        modelsAndQuantities: '',
        opportunitySize: null,
        extensionJustification: '',
      },
      priceDesk: {
        revenueTd: null,
        targetTd: null,
        numberOfExtensions: null,
        supportAverage: null,
        pdJustification: '',
      },
      snackbar: {
        show: false,
        color: 'success',
        message: ''
      }
    };
  },
  computed: {
    requestId() {
      const id = Number(this.$route.params.id);
      return isNaN(id) ? null : id;
    },
    revenueVsTarget() {
      const revenue = parseFloat(this.priceDesk.revenueTd);
      const target = parseFloat(this.priceDesk.targetTd);
      if (!isNaN(revenue) && !isNaN(target) && target !== 0) {
        return (revenue / target).toFixed(2);
      }
      if (target === 0 && revenue > 0) {
        return '#DIV/0!';
      }
      return '';
    },
  },
  mounted() {
    this.loadExtension();
  },
  methods: {
    async loadExtension() {
      const requestId = Number(this.$route.params.id);
      if (!requestId) return;
      try {
        this.loading = true;
        const { data } = await getCmacExtension(requestId);
        if (data) this.mapApiToLocal(data);
      } catch (e) {
        // Silently ignore if not found; surface other errors in console
        console.error('Failed to load CMAC extension', e);
      } finally {
        this.loading = false;
      }
    },
    mapApiToLocal(apiData) {
      // Top-level info (safe mapping if keys exist)
      if (apiData.customer) {
        this.formData.customerName = apiData.customer.displayName || apiData.customer.businessName || '';
      }
      if (apiData.cmacNumber) this.formData.cmacNumber = apiData.cmacNumber;
      if (apiData.isMsa) this.formData.msa = apiData.isMsa === 'Y' ? 'YES' : 'NO';
      if (apiData.extensionInMonth != null) this.formData.extensionLength = String(apiData.extensionInMonth);
      if (apiData.modelsAndQuantities) this.formData.modelsAndQuantities = apiData.modelsAndQuantities;
      if (apiData.opportunitySize != null) this.formData.opportunitySize = apiData.opportunitySize;
      if (apiData.salesJustificationText) this.formData.extensionJustification = apiData.salesJustificationText;

      // Price desk (read-only) fields
      if (apiData.revenueTd != null) this.priceDesk.revenueTd = apiData.revenueTd;
      if (apiData.targetTd != null) this.priceDesk.targetTd = apiData.targetTd;
      if (apiData.noOfExtensionTd != null) this.priceDesk.numberOfExtensions = apiData.noOfExtensionTd;
      if (apiData.supportAverage != null) this.priceDesk.supportAverage = apiData.supportAverage;
      if (apiData.priceDeskJustificationText) this.priceDesk.pdJustification = apiData.priceDeskJustificationText;
    },
    async saveExtension() {
      const requestId = Number(this.$route.params.id);
      if (!requestId) return;
      const payload = {
        requestId,
        isMsa: this.formData.msa === 'YES' ? 'Y' : 'N',
        extensionInMonth: parseInt(this.formData.extensionLength, 10) || 0,
        salesJustificationText: this.formData.extensionJustification || '',
        revenueTd: parseFloat(this.priceDesk.revenueTd) || 0,
        targetTd: parseFloat(this.priceDesk.targetTd) || 0,
        noOfExtensionTd: parseInt(this.priceDesk.numberOfExtensions, 10) || 0,
        supportAverage: this.priceDesk.supportAverage ?? '',
        priceDeskJustificationText: this.priceDesk.pdJustification ?? '',
      };
      try {
        this.saving = true;
        await saveCmacExtension(payload);
        this.snackbar = { show: true, color: 'success', message: 'CMAC extension saved successfully.' };
      } catch (e) {
        console.error('Failed to save CMAC extension', e);
        this.snackbar = { show: true, color: 'error', message: 'Failed to save CMAC extension.' };
      } finally {
        this.saving = false;
      }
    }
  }
};
</script>

<style scoped>
.v-card-title {
  background-color: #f5f5f5;
  color: red;
  font-weight: bold;
}
.font-weight-bold {
    font-weight: bold;
}
.black.white--text {
    background-color: black !important;
    color: white !important;
}
.v-row {
    border-bottom: 1px solid #ccc;
    margin-bottom: 8px;
    padding-bottom: 8px;
}
.v-col {
    display: flex;
    align-items: center;
}
</style>
