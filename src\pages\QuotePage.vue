<template>
      <v-tabs v-model="activeTab" grow class="mb-4 canon-tabs"  bg-color="canon">
        <v-tab value="pricingTab">Approved Pricing</v-tab>
        <v-tab value="requestDetails">Request Details</v-tab>
    </v-tabs>
    <v-window v-model="activeTab" class="pnl-window">
    <v-window-item value="pricingTab">
      <v-container fluid>
        <!-- Header Section -->
        <v-row class="mb-4 align-center">
          <v-col cols="12" md="2" class="flex-grow-0">
            <v-img src="/canon-logo.jpg" alt="Canon Logo" contain height="50"></v-img>
          </v-col>
          <v-col cols="12" md="7">
            <h2 class="text-h5">{{ data.documentHeader.customerName }}</h2>
            <v-row align="center" class="text-caption mt-1">
              <v-col cols="auto" v-if="cmacAccount" class="py-0 pr-2 pl-0">
                <span>CMAC Account: {{ cmacAccount }}</span>
              </v-col>
              <v-col cols="auto" v-if="cmacAccount" class="py-0 px-2">
                <v-divider vertical></v-divider>
              </v-col>
              <v-col cols="auto" class="py-0 px-2 pl-0">
                <span>Pricing Issued: {{ formatDate(new Date()) }}</span>
              </v-col>
              <v-col cols="auto" class="py-0 px-2">
                <v-divider vertical></v-divider>
              </v-col>
              <v-col cols="auto" class="py-0 px-2 pl-0 d-flex align-center">
                <span class="mr-2">Price Validity:</span>
                <v-text-field
                  v-model="priceValidityDate"
                  type="date"
                  variant="outlined"
                  density="compact"
                  hide-details
                  style="max-width: 170px;"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-chip v-if="data.documentHeader.competitiveKO"
                    :color="data.documentHeader.competitiveKO.status === 'NO' ? 'green' : 'red'"
                    size="small" class="mt-2">
              Competitive KO: {{ data.documentHeader.competitiveKO.status }}
              <v-tooltip activator="parent" location="bottom">{{ data.documentHeader.competitiveKO.details }}</v-tooltip>
            </v-chip>
          </v-col>
        <v-col cols="12" md="3" class="text-md-right">
          <v-card variant="outlined" class="pa-2">
            <div class="text-caption">Total MSRP</div>
            <div class="text-h6 font-weight-bold">{{ formatCurrency(data.summaryTotals.totalMSRP) }}</div>
            <div class="text-caption mt-1">Total Approved Pricing</div>
            <div class="text-h6 font-weight-bold">{{ formatCurrency(data.summaryTotals.totalApprovedPricing) }}</div>
          </v-card>
          <div class="d-flex flex-column mt-2" style="gap: 8px;">
            <v-btn 
              color="success" 
              @click="onApprove" 
              prepend-icon="check"
              size="large"
              :loading="isSubmitting"
              :disabled="isSubmitting || !canApprove"
            >
              Approve Pricing
            </v-btn>
            <v-btn 
              color="primary" 
              @click="exportQuote" 
              prepend-icon="download"
              variant="outlined"
            >
              Export to Excel
            </v-btn>
          </div>
        </v-col>
      </v-row>

      <!-- Key Instructions / Price Desk Comments -->
      <v-row class="align-center mb-4">
        <!-- <v-col cols="12" md="6">
          <h1 class="text-h4 font-weight-bold">{{ data.documentHeader.requestName }}</h1>
          <p class="text-subtitle-1 text-medium-emphasis">SR# {{ data.documentHeader.requestId }}</p>
        </v-col> -->
        <v-col cols="12" md="6">
          <v-select
            v-model="selectedCategory"
            :items="categoryOptions"
            item-title="description"
            item-value="id"
            label="Category"
            required
            :rules="[v => !!v || 'Category is required']"
            variant="underlined"
            @update:model-value="handleCategoryChange"
            hide-details="auto"
          ></v-select>
        </v-col>
      </v-row>

      <!-- Key Instructions / Price Desk Comments -->
      <v-row>
        <v-col cols="12">
          <v-textarea
            v-model="priceDeskComments"
            label="Price Desk Comments / Key Instructions"
            variant="outlined"
            rows="2"
            auto-grow
            :maxlength="500"
            class="mt-4"
          ></v-textarea>
        </v-col>
      </v-row>

      <!-- Product Configurations -->
      <section-card title="Product Configuration">
        <v-expansion-panels v-model="expandedProductPanels" multiple class="mt-2">
          <v-expansion-panel
            v-for="group in data.productGroups"
            :key="group.id"
            :title="group.groupName"
          >
            <v-expansion-panel-text>
              <v-table density="compact" class="mb-2">
                <thead>
                  <tr>
                    <th class="text-left">Product Description</th>
                    <th class="text-left">Item No.</th>
                    <th class="text-right">DSD Qty</th>
                    <th class="text-right">Dealer territory Qty</th>
                    <th class="text-right">Total Qty</th>
                    <th class="text-right">MSRP/Unit</th>
                    <th class="text-right">Approved Price/Unit</th>
                    <th class="text-right">Approved (% MSRP)</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Main Item -->
                  <tr class="font-weight-bold bg-grey-lighten-4">
                    <td>{{ group.mainItem.productDescription }}</td>
                    <td>{{ group.mainItem.itemNumber }}</td>
                    <td class="text-right">{{ group.mainItem.dsdQty }}</td>
                    <td class="text-right">{{ group.mainItem.dealerTerritoryQty !== undefined ? group.mainItem.dealerTerritoryQty : '-' }}</td>
                    <td class="text-right">{{ group.mainItem.totalQty }}</td>
                    <td class="text-right">{{ formatCurrency(group.mainItem.msrp) }}</td>
                    <td class="text-right">{{ formatCurrency(group.mainItem.approvedSellingPrice) }}</td>
                    <td class="text-right">{{ group.mainItem.approvedSellingPricePercentMSRP }}%</td>
                  </tr>
                  <!-- Accessories -->
                  <tr v-for="(acc, index) in group.accessories" :key="index">
                    <td class="pl-6">{{ acc.productDescription }}</td>
                    <td>{{ acc.itemNumber }}</td>
                    <td class="text-right">{{ acc.dsdQty }}</td>
                    <td class="text-right">{{ acc.dealerTerritoryQty !== undefined ? acc.dealerTerritoryQty : '-' }}</td>
                    <td class="text-right">{{ acc.totalQty }}</td>
                    <td class="text-right">{{ formatCurrency(acc.msrp) }}</td>
                    <td class="text-right">{{ formatCurrency(acc.approvedSellingPrice) }}</td>
                    <td class="text-right">{{ acc.approvedSellingPricePercentMSRP }}%</td>
                  </tr>
                  <!-- Group Totals -->
                  <tr class="font-weight-bold bg-blue-grey-lighten-5">
                    <td colspan="5" class="text-right">Group Totals:</td>
                    <td class="text-right">{{ formatCurrency(group.groupTotal.msrp) }}</td>
                    <td class="text-right">{{ formatCurrency(group.groupTotal.approvedSellingPrice) }}</td>
                    <td class="text-right">{{ group.groupTotal.approvedSellingPricePercentMSRP !== undefined ? group.groupTotal.approvedSellingPricePercentMSRP + '%' : '-' }}</td>
                  </tr>
                </tbody>
              </v-table>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </section-card>

      <!-- Software Licensing -->
      <section-card v-if="data.softwareLicensing.length > 0" title="Software Licensing">
        <v-expansion-panels v-model="expandedSoftwarePanels" multiple class="mt-2">
          <v-expansion-panel
            v-for="category in data.softwareLicensing"
            :key="category.id"
            :title="category.category"
          >
            <v-expansion-panel-text>
              <v-table density="compact" class="mb-2">
                <thead>
                  <tr>
                    <th class="text-left">Product Description</th>
                    <th class="text-left">Item No.</th>
                    <th class="text-right">DSD Qty</th>
                    <th class="text-right">Dealer territory Qty</th>
                    <th class="text-right">Total Qty</th>
                    <th class="text-right">MSRP/Unit</th>
                    <th class="text-right">Approved Price/Unit</th>
                    <th class="text-right">Approved (% MSRP)</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in category.items" :key="index">
                    <td>{{ item.productDescription }}</td>
                    <td>{{ item.itemNumber }}</td>
                    <td class="text-right">{{ item.dsdQty }}</td>
                    <td class="text-right">{{ item.dealerTerritoryQty !== undefined ? item.dealerTerritoryQty : '-' }}</td>
                    <td class="text-right">{{ item.totalQty }}</td>
                    <td class="text-right">{{ formatCurrency(item.msrp) }}</td>
                    <td class="text-right">{{ formatCurrency(item.requestedSellingPricePL) }}</td>
                    <td class="text-right">{{ item.approvedSellingPricePercentMSRP }}%</td>
                  </tr>
                  <!-- Category Totals -->
                  <tr class="font-weight-bold bg-blue-grey-lighten-5">
                    <td colspan="5" class="text-right">Category Totals:</td>
                    <td class="text-right">{{ formatCurrency(category.categoryTotal.msrp) }}</td>
                    <td class="text-right">{{ formatCurrency(category.categoryTotal.requestedSellingPricePL) }}</td>
                    <td class="text-right">{{ category.categoryTotal.approvedSellingPricePercentMSRP }}%</td>
                  </tr>
                </tbody>
              </v-table>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </section-card>

      <!-- Terms and Conditions for Hardware Price Program -->
      <section-card title="Terms and conditions for the hardware price program">
        <v-list density="compact">
          <v-list-item>
            <v-list-item-title>Minimum commitment:</v-list-item-title>
            <v-list-item-subtitle>
              {{ formatCurrency(data.hardwarePriceProgramTerms.minimumCommitment) }}
            </v-list-item-subtitle>
          </v-list-item>
          <v-list-item>
            <v-list-item-title>Chargebacks:</v-list-item-title>
            <v-list-item-subtitle>{{ data.hardwarePriceProgramTerms.chargebacks }}</v-list-item-subtitle>
          </v-list-item>
          <v-list-item>
            <v-list-item-title>Validity:</v-list-item-title>
            <v-list-item-subtitle>{{ formatDate(data.hardwarePriceProgramTerms.validity) }}</v-list-item-subtitle>
          </v-list-item>
          <v-list-item>
            <v-list-item-title>CMAC Account number:</v-list-item-title>
            <v-list-item-subtitle>{{ data.hardwarePriceProgramTerms.cmacAccountNumber }}</v-list-item-subtitle>
          </v-list-item>
          <!-- <v-list-item>
            <v-list-item-title>Equipment eligibility:</v-list-item-title>
            <v-list-item-subtitle>{{ data.hardwarePriceProgramTerms.equipmentEligibility }}</v-list-item-subtitle>
          </v-list-item> -->
          <v-list-item>
            <v-list-item-title>Equipment exclusions:</v-list-item-title>
            <v-list-item-subtitle>{{ data.hardwarePriceProgramTerms.equipmentExclusions }}</v-list-item-subtitle>
          </v-list-item>
        </v-list>
      </section-card>

      <!-- DSD Territory Service Rates -->
      <section-card title="DSD Territory Service Rates">
        <!-- Note: The keyInstruction, serviceValuePack, fixedServiceRateTerm, and colourModels table are already here -->
        <v-textarea
          v-model="serviceComments"
          label="Service Approval Comments"
          variant="outlined"
          rows="2"
          auto-grow
          :maxlength="500"
          class="mb-3"
        ></v-textarea>
        <v-alert density="compact" type="info" variant="tonal" class="mb-3 text-caption" border="start">
          Please Note: Service Options CANNOT be Combined. All Machines MUST be priced entirely under ONE service option.
        </v-alert>
        <v-row class="my-3 align-center">
          <v-col cols="auto">
            <span class="font-weight-bold text-subtitle-1 mr-1">Service Value Pack:</span>
            <v-chip color="primary" variant="elevated" size="large">{{ data.dsdTerritoryServiceRates.serviceValuePack }}</v-chip>
          </v-col>
          <v-col cols="auto">

          </v-col>
        </v-row>
        <!-- Render a separate table for each Fixed Service Rate Term -->
        <div v-for="(models, term) in approvalsByTerm" :key="term" class="mb-6">
          <v-row class="my-3 align-center">
            <v-col cols="auto">
              <span class="font-weight-bold text-subtitle-1 mr-1">Fixed Service Rate Term:</span>
              <span class="font-weight-bold text-subtitle-1">{{ term }} Months</span>
            </v-col>
          </v-row>
          <v-table density="compact" class="mt-2">
            <thead>
              <tr>
                <th>Model Name</th>
                <th class="text-right">Machine Qty</th>
                <th class="text-right">Std B/W CPC</th>
                <th class="text-right">Std Colour CPC</th>
                <!-- <th class="text-right">Oversize B/W Rate (per unit)</th> -->
                <th class="text-right">Extra Long Colour Rate (per unit)</th>
                <th class="text-center">Minimums Applicable</th>
                <th class="text-center">Accessory Fees Included in CPC</th>
                <th class="text-left">Base $Amt / Colour Volume</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="model in models" :key="model.modelName">
                <td>{{ model.modelName }}</td>
                <td class="text-right">{{ model.machineQuantity }}</td>
                <td class="text-right">{{ formatSmallCurrency(model.standardBWCpcRate) }}</td>
                <td class="text-right">{{ model.standardColourCpcRate ? formatSmallCurrency(model.standardColourCpcRate) : '-' }}</td>
                <!-- <td class="text-right">{{ model.extraLongBwRate ? formatSmallCurrency(model.extraLongBwRate) : '-' }}</td> -->
                <td class="text-right">{{ model.extraLongColourRate ? formatSmallCurrency(model.extraLongColourRate) : '-' }}</td>
                <td class="text-center">{{ model.minimumsApplicable }}</td>
                <td class="text-center">{{ model.accessoryFeesIncludedInCPC }}</td>
                <td class="text-left">
                  {{ model.baseAmount ? model.baseAmount : '-' }}/{{ model.baseColourVolume ? model.baseColourVolume : '-' }}
                </td>
              </tr>
            </tbody>
          </v-table>
        </div>


        <!-- Extra Long Life Note (Purple Box from Excel) -->
        <v-alert v-if="data.dsdTerritoryServiceRates.extraLongLifeNote" type="info" variant="tonal" class="mt-4 mb-3 text-caption" border="start" prominent>
          {{ data.dsdTerritoryServiceRates.extraLongLifeNote }}
        </v-alert>

        <!-- Additional Service Fees [commented for now] -->
        <!-- <v-card v-if="additionalServiceFees.length > 0" variant="outlined" class="mt-4">
          <v-card-title class="text-subtitle-2 font-weight-medium d-flex justify-space-between align-center">
            <span>Additional Service Fees</span>
            <v-btn color="primary" size="small" variant="text" prepend-icon="add" @click="addAdditionalServiceFee">Add Fee</v-btn>
          </v-card-title>
          <v-card-text class="pa-0">
            <v-table density="compact">
              <thead>
                <tr>
                  <th>Description</th>
                  <th class="text-right">Amount</th>
                  <th class="text-center" style="width: 50px;"></th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(fee, index) in additionalServiceFees" :key="index">
                  <td>
                    <v-text-field v-model="fee.description" density="compact" variant="plain" hide-details placeholder="Enter description"></v-text-field>
                  </td>
                  <td class="text-right">
                    <v-text-field v-model.number="fee.amount" type="number" density="compact" variant="plain" hide-details placeholder="0.00" min="0" step="0.01" @update:model-value="updateAdditionalServiceFeesTotal">
                      <template #prepend-inner><span class="text-medium-emphasis">$</span></template>
                    </v-text-field>
                  </td>
                  <td class="text-center">
                    <v-btn icon="delete" variant="text" density="comfortable" size="small" @click="removeAdditionalServiceFee(index)"></v-btn>
                  </td>
                </tr>
                <tr>
                  <td class="text-right font-weight-medium">Total:</td>
                  <td class="text-right font-weight-medium">{{ formatCurrency(additionalServiceFeesTotal) }}</td>
                  <td></td>
                </tr>
              </tbody>
            </v-table>
          </v-card-text>
        </v-card>
        <div v-else class="mt-4">
            <v-btn color="primary" size="small" variant="text" prepend-icon="add" @click="addAdditionalServiceFee">Add Additional Service Fee</v-btn>
        </div> -->

        <!-- Colour Model Terms & Conditions -->
        <v-card variant="tonal" class="mt-4">
          <v-card-title class="text-subtitle-2 font-weight-medium">Colour Model Terms & Conditions</v-card-title>
          <v-card-text>
            <v-list density="compact" class="text-caption bg-transparent">
              <v-list-item v-for="(term, index) in data.colourModelTermsAndConditions" :key="index" class="pa-1">
                <template v-slot:prepend>
                  <v-icon size="x-small" class="mr-2">circle</v-icon>
                </template>
                <v-list-item-title style="white-space: pre-line;">{{ term.text || term }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>

        <!-- General Service Related Terms & Conditions -->
        <v-card variant="tonal" class="mt-4">
          <v-card-title class="text-subtitle-2 font-weight-medium">General Service Related Terms & Conditions</v-card-title>
          <v-card-text>
            <v-list density="compact" class="text-caption bg-transparent">
              <v-list-item v-for="(term, index) in data.generalServiceRelatedTermsAndConditions" :key="index" class="pa-1">
                <template v-slot:prepend>
                  <v-icon size="x-small" class="mr-2">circle</v-icon>
                </template>
                <v-list-item-title style="white-space: pre-line;">{{ term.text || term }}</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>

      </section-card>





      <!-- Lease Rate Factors -->
      <section-card title="Lease Rate Factors" class="mt-4">
        <!-- Input Parameters Section -->
        <v-card variant="outlined" class="mb-4">
          <v-card-title class="text-subtitle-2 bg-grey-lighten-4">Input Parameters</v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="4">
                <v-text-field
                  v-model.number="yieldRate"
                  label="Yield"
                  type="number"
                  step="0.01"
                  variant="outlined"
                  density="compact"
                  suffix="%"
                  :rules="[v => v >= 0 || 'Must be non-negative']"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <v-text-field
                  v-model.number="residualRate"
                  label="Residual"
                  type="number"
                  step="0.01"
                  variant="outlined"
                  density="compact"
                  suffix="%"
                  :rules="[v => v >= 0 && v <= 100 || 'Must be between 0-100']"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="4">
                <div class="d-flex align-center" style="gap: 8px;">
                  <v-text-field
                    v-model.number="newMonthInput"
                    label="Add Months"
                    type="number"
                    step="1"
                    min="1"
                    max="120"
                    variant="outlined"
                    density="compact"
                    hide-details
                    style="flex: 1;"
                    @keyup.enter="addCustomMonth"
                  ></v-text-field>
                  <v-btn
                    color="primary"
                    variant="outlined"
                    density="compact"
                    @click="addCustomMonth"
                    :disabled="!newMonthInput || newMonthInput <= 0 || allLrfTerms.includes(newMonthInput)"
                  >
                    Add
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>

        <!-- Lease Rate Factors Table -->
        <v-table density="compact" class="mt-2 text-caption">
          <thead>
            <tr>
              <th class="text-left" style="width: 20%;">LRF Type</th>
              <th class="text-center" v-for="termData in leaseRateFactorsTable" :key="termData.term" style="width: 20%;">
                <div class="d-flex flex-column align-center">
                  <div>{{ termData.term }} Months</div>
                  <div>Equipment</div>
                  <v-btn
                    v-if="customLrfTerms.includes(termData.term)"
                    icon="close"
                    variant="text"
                    density="compact"
                    size="x-small"
                    color="error"
                    @click="removeCustomMonth(termData.term)"
                    class="mt-1"
                  ></v-btn>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-left font-weight-bold">LRF - quarterly ($ per $1,000)</td>
              <td class="text-center" v-for="termData in leaseRateFactorsTable" :key="'q-' + termData.term">
                {{ termData.quarterlyLRF.toFixed(2) }}
              </td>
            </tr>
            <tr>
              <td class="text-left font-weight-bold">LRF - monthly ($ per $1,000)</td>
              <td class="text-center" v-for="termData in leaseRateFactorsTable" :key="'m-' + termData.term">
                {{ termData.monthlyLRF.toFixed(2) }}
              </td>
            </tr>
            <tr>
              <td class="text-left font-weight-bold">Term (months)</td>
              <td class="text-center" v-for="termData in leaseRateFactorsTable" :key="'t-' + termData.term">
                {{ termData.term }}
              </td>
            </tr>
            <tr>
              <td class="text-left font-weight-bold">Residual</td>
              <td class="text-center" v-for="termData in leaseRateFactorsTable" :key="'r-' + termData.term">
                {{ residualRate.toFixed(2) }}%
              </td>
            </tr>
          </tbody>
        </v-table>

        <!-- Calculated Fields Section -->
        <v-card variant="outlined" class="mt-4">
          <v-card-title class="text-subtitle-2 bg-grey-lighten-4">Calculated Fields</v-card-title>
          <v-card-text>
            <v-table density="compact" class="text-caption">
              <thead>
                <tr>
                  <th class="text-left" style="width: 20%;">Field</th>
                  <th class="text-center" v-for="termData in leaseRateFactorsTable" :key="'calc-' + termData.term" style="width: 20%;">
                    <div class="d-flex flex-column align-center">
                      <div>{{ termData.term }} Months</div>
                      <v-btn
                        v-if="customLrfTerms.includes(termData.term)"
                        icon="close"
                        variant="text"
                        density="compact"
                        size="x-small"
                        color="error"
                        @click="removeCustomMonth(termData.term)"
                        class="mt-1"
                      ></v-btn>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="text-left font-weight-bold">Yield</td>
                  <td class="text-center" v-for="termData in leaseRateFactorsTable" :key="'yield-' + termData.term">
                    {{ yieldRate.toFixed(2) }}%
                  </td>
                </tr>
                <tr>
                  <td class="text-left font-weight-bold">COF</td>
                  <td class="text-center" v-for="termData in leaseRateFactorsTable" :key="'cof-' + termData.term">
                    {{ cofRate.toFixed(2) }}%
                  </td>
                </tr>
                <tr>
                  <td class="text-left font-weight-bold">PMT</td>
                  <td class="text-center" v-for="termData in leaseRateFactorsTable" :key="'pmt-' + termData.term">
                    ${{ Math.abs(termData.pmtQuarterly).toFixed(0) }}
                  </td>
                </tr>
                <tr>
                  <td class="text-left font-weight-bold">PV at COF</td>
                  <td class="text-center" v-for="termData in leaseRateFactorsTable" :key="'pv-' + termData.term">
                    ${{ Math.abs(termData.pvAtCOFQuarterly).toFixed(0) }}
                  </td>
                </tr>
                <tr class="bg-yellow-lighten-4">
                  <td class="text-left font-weight-bold">Origination Fee</td>
                  <td class="text-center font-weight-bold" v-for="termData in leaseRateFactorsTable" :key="'orig-' + termData.term">
                    ${{ Math.abs(termData.originationFeeQuarterly).toFixed(0) }}
                  </td>
                </tr>
              </tbody>
            </v-table>
          </v-card-text>
        </v-card>

        <!-- Current LRF Values -->
        <div class="mt-4">
          <v-row>
            <v-col cols="12" md="4">
              <v-select
                v-model="selectedLrfTerm"
                :items="allLrfTerms"
                label="Select Months"
                variant="outlined"
                density="compact"
                hide-details
              ></v-select>
            </v-col>
            <v-col cols="12" md="4">
              <v-text-field
                :model-value="currentQuarterlyLRF.toFixed(2)"
                label="Quarterly LRF (Calculated)"
                variant="outlined"
                density="compact"
                readonly
                bg-color="grey-lighten-5"
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="4">
              <v-text-field
                :model-value="currentMonthlyLRF.toFixed(2)"
                label="Monthly LRF (Calculated)"
                variant="outlined"
                density="compact"
                readonly
                bg-color="grey-lighten-5"
              ></v-text-field>
            </v-col>
          </v-row>
        </div>

        <v-textarea
          v-model="leaseRateFactorComments"
          label="Lease Rate Factors Comments"
          variant="outlined"
          rows="2"
          :maxlength="500"
          auto-grow
          class="mt-4"
        ></v-textarea>
      </section-card>

      <!-- Soft Costs -->
      <section-card title="Soft Costs" class="mt-4">
        <v-alert type="info" variant="tonal" class="mb-2 text-caption" border="start" v-if="data.softCosts.applicabilityNote">
          {{ data.softCosts.applicabilityNote }}
        </v-alert>
        <v-alert type="warning" variant="tonal" class="mb-3 text-caption" border="start" v-if="data.softCosts.deliveryFeesNote">
          {{ data.softCosts.deliveryFeesNote }}
        </v-alert>
        <v-card variant="outlined" class="my-3">
          <v-card-text class="pa-0">
            <v-table density="compact" class="text-caption">
              <thead>
                <tr>
                  <th class="text-left" style="width: 35%;">Model</th>
                  <th v-for="(item, colIndex) in data.softCosts.costItems" :key="colIndex" class="text-center">
                    {{ item.description }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(model, rowIndex) in selectedModels" :key="rowIndex">
                  <td>
                    <v-select
                      v-model="selectedModels[rowIndex]"
                      :items="modelOptions"
                      label="Select Item"
                      density="compact"
                      variant="underlined"
                      hide-details
                      return-object
                      item-title="label"
                      item-value="value"
                      @update:model-value="addNewColumnIfNeeded"
                      class="model-selector py-2"
                    ></v-select>
                  </td>
                  <td 
                    v-for="(item, colIndex) in data.softCosts.costItems" 
                    :key="`${rowIndex}-${colIndex}`"
                    class="text-center"
                  >
                    <v-text-field
                      v-if="model"
                      :key="`price-${model.value}-${item.id}`"
                      :model-value="getDisplayValueForSoftCost(item, model.value)"
                      type="number"
                      density="compact"
                      variant="underlined"
                      hide-details
                      @update:model-value="(val) => updateSoftCostPrice(model.value, item.id, val)"
                      class="text-center"
                      prefix="$"
                      step="0.01"
                    ></v-text-field>
                    <span v-else>-</span>
                  </td>
                </tr>
                <!-- <tr v-if="selectedModels.length < modelOptions.length">
                  <td>
                    <v-btn
                      icon="add"
                      variant="text"
                      density="comfortable"
                      @click="addNewColumn"
                      size="small"
                    ></v-btn>
                  </td>
                  <td v-for="(item, colIndex) in data.softCosts.costItems" :key="`add-${colIndex}`" class="text-center"></td>
                </tr> -->
              </tbody>
            </v-table>
          </v-card-text>
        </v-card>


          
        <!-- Additional Delivery Fees -->
        <v-card variant="outlined" class="mb-4">
          <v-card-title class="text-subtitle-2 font-weight-medium">Additional Delivery Fees</v-card-title>
          <v-card-text class="pa-0">
            <v-table density="compact">
              <thead>
                <tr>
                  <th>Item</th>
                  <th class="text-right">Cost</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(charge, idx) in data.additionalCharges" :key="charge.unitName + '-' + idx">
                  <td>{{ charge.unitName }}</td>
                  <td class="text-right">
                    <v-text-field
                      v-model.number="charge.price"
                      type="number"
                      density="compact"
                      variant="plain"
                      hide-details
                      min="0"
                      step="0.01"
                      class="text-right fee-input"
                      suffix="$"
                    ></v-text-field>
                  </td>
                </tr>
              </tbody>
            </v-table>
          </v-card-text>
        </v-card>

        <!-- Other Fees -->
        <v-card variant="outlined" class="mb-4">
          <v-card-title class="text-subtitle-2 font-weight-medium d-flex justify-space-between align-center">
            <span>Other Fees</span>
            <v-btn
              color="primary"
              size="small"
              variant="text"
              prepend-icon="add"
              @click="addOtherFee"
            >
              Add Fee
            </v-btn>
          </v-card-title>
          <v-card-text class="pa-0">
            <v-table density="compact">
              <thead>
                <tr>
                  <th>Item</th>
                  <th class="text-right">Cost</th>
                  <th class="text-center" style="width: 50px;"></th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(fee, index) in otherFees" :key="index">
                  <td>
                    <v-text-field
                      v-model="fee.unitName"
                      density="compact"
                      variant="plain"
                      hide-details
                      placeholder="Enter item name"
                    ></v-text-field>
                  </td>
                  <td class="text-right">
                    <v-text-field
                      v-model.number="fee.price"
                      type="number"
                      density="compact"
                      variant="plain"
                      hide-details
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                      class="fee-input"
                      @update:model-value="updateOtherFeesTotal"
                      suffix="$"
                    ></v-text-field>
                  </td>
                  <td class="text-center">
                    <v-btn
                      icon="delete"
                      variant="text"
                      density="comfortable"
                      size="small"
                      @click="removeOtherFee(index)"
                    ></v-btn>
                  </td>
                </tr>
                <tr v-if="otherFees.length > 0">
                  <td class="text-right font-weight-medium">Total:</td>
                  <td class="text-right font-weight-medium">{{ formatCurrency(otherFeesTotal) }}</td>
                  <td></td>
                </tr>
                <tr v-if="otherFees.length === 0">
                  <td colspan="3" class="text-center text-medium-emphasis py-4">
                    No other fees added yet. Click 'Add Fee' to add one.
                  </td>
                </tr>
              </tbody>
            </v-table>
          </v-card-text>
        </v-card>

        <p v-if="data.softCosts.generalEquipmentNote" class="text-caption mt-2"><em>{{ data.softCosts.generalEquipmentNote }}</em></p>
        <div v-if="data.softCosts.detailedNotes && data.softCosts.detailedNotes.length > 0" class="mt-3">
          <p v-for="note in data.softCosts.detailedNotes" :key="note.noteId" class="text-caption">
            <strong>Note {{ note.noteId }}:</strong> {{ note.text }}
          </p>
        </div>

     

        <!-- General Notes and Conditions (Moved from Footer) -->
        <v-card variant="tonal" class="mt-4" v-if="false">
          <v-card-title class="text-subtitle-2 font-weight-medium">General Notes & Conditions</v-card-title>
          <v-card-text>
            <div v-for="(noteBlock, index) in data.footerNotes" :key="index" class="mb-3">
              <p v-if="noteBlock.title" class="text-subtitle-2 font-weight-bold">{{ noteBlock.title }}</p>
              <p class="text-caption" style="white-space: pre-line;">{{ noteBlock.text }}</p>
            </div>
          </v-card-text>
        </v-card>
      </section-card>
      <!-- Notifications -->
      <v-snackbar v-model="snackbar.show" :color="snackbar.color" location="bottom right" timeout="3000">
        {{ snackbar.message }}
      </v-snackbar>
    </v-container>
    </v-window-item>
    <v-window-item value="requestDetails">
      <PreviewTab ref="previewTabRef" :request-id="props.id" />
    </v-window-item>
  </v-window>
</template>

<script setup >
import { ref, computed, onMounted,watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getApprovedPricing, getSoftCostRates, getCategoryDetails, postApprovedPricing } from '@/lib/api';
import { quoteData as dummyQuoteData } from '@/data/dummy-quote-data';
import SectionCard from '@/components/SectionCard.vue';
import { getLov, LovCategories } from '@/services/lovService';

import { useAppStore } from '@/stores/AppStore';
import PreviewTab from '@/components/sales/PreviewTab.vue';
import { onUpdated } from 'vue';
// --- STATE MANAGEMENT ---

const route = useRoute();
const appStore = useAppStore();
const router = useRouter();

// Text areas
const priceDeskComments = ref('');
const internalComments = ref('');
const categoryOptions = ref([]);
const selectedCategory = ref(null);
const serviceComments = ref('');
const leaseRateFactorComments = ref('');
const priceValidityDate = ref('');
const quarterlyLRF = ref(0);
const monthlyLRF = ref(0);
// Lease Rate Factors term selection
const defaultLrfTerms = [36, 48, 60, 66];
const customLrfTerms = ref([]);
const selectedLrfTerm = ref(36);

// Expansion panels state
const expandedProductPanels = ref([]);
const expandedSoftwarePanels = ref([]);

// Soft cost edited prices - stored as { modelId: { costItemId: editedValue } }
const editedSoftCostPrices = ref({});
const newMonthInput = ref(null);

// Lease Rate Factors input fields (Deal Value and COF are now fixed defaults)
const dealValue = 1000; // Fixed default $1,000
const yieldRate = ref(11.00); // Default 11.00%
const cofRate = 6.87; // Fixed default 6.87% (Cost of Funds)
const residualRate = ref(0); // Default 0%
const paymentFrequency = ref('Q'); // 'Q' for Quarterly, 'M' for Monthly
const isSubmitting = ref(false);
const snackbar = ref({ show: false, message: '', color: 'success' });

// Interactive UI sections
 
const additionalServiceFees = ref([]);

const otherFees = ref([]);
const activeTab = ref('pricingTab');
const previewTabRef = ref(null);

const selectedModels = ref([{ value: null, label: 'Select Item' }]); // For Soft Costs table
const modelOptions = ref([]); // Options for the soft costs model selector

// Map of modelCode => soft cost response
const softCostRates = ref({});
const cmacAccount = computed(() =>
  (data.value?.documentHeader?.cmacAccountNumber) ||
  (data.value?.hardwarePriceProgramTerms?.cmacAccountNumber) || ''
);

const isValidDate = (val) => /\d{4}-\d{2}-\d{2}/.test(val || '');
const canApprove = computed(() => {
  const dateOk = Boolean(priceValidityDate.value && isValidDate(priceValidityDate.value));
  const categoryOk = selectedCategory.value !== null && selectedCategory.value !== undefined && selectedCategory.value !== '';
  return dateOk && categoryOk;
});
const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});
// Main data object, initialized with default structure to prevent template errors
// Initialize with only the sections we still rely on from the dummy file so the template has immediate data
const data = ref({
  ...{

    // footerNotes: dummyQuoteData.footerNotes,
    additionalCharges: [],
    softCosts: {
      applicabilityNote: "These soft costs are applicable for DSD territory install only. For agent territory, please contact your agent for their specific soft costs. Soft costs are not discountable.",
      deliveryFeesNote: "**Delivery Fees are based on the location specified on the first page of this document. Additional charges may apply for remote locations or special delivery requirements.",
      costItems: dummyQuoteData.softCosts.costItems.map((item, index) => ({
        ...item,
        id: item.id || `cost-item-${index}`
      })),
      generalEquipmentNote: dummyQuoteData.softCosts.generalEquipmentNote,
      detailedNotes: dummyQuoteData.softCosts.detailedNotes,
      colourModelTermsAndConditions: dummyQuoteData.colourModelTermsAndConditions,
      generalServiceRelatedTermsAndConditions: dummyQuoteData.generalServiceRelatedTermsAndConditions,
    },
    colourModelTermsAndConditions: dummyQuoteData.colourModelTermsAndConditions,
    generalServiceRelatedTermsAndConditions: dummyQuoteData.generalServiceRelatedTermsAndConditions,
  },
  // The rest will be filled / overridden by API

  documentHeader: {},
  summaryTotals: { totalMSRP: 0, totalApprovedPricing: 0 },
  productGroups: [],
  softwareLicensing: [],
  hardwarePriceProgramTerms: { minimumCommitment: {} },
  dsdTerritoryServiceRates: { serviceValuePack: '', approvals: [] },
  leaseRateFactors: { terms: [], rates: [] },
  // populated from dummyQuoteData above
});

// --- COMPUTED PROPERTIES ---

const additionalServiceFeesTotal = computed(() =>
  additionalServiceFees.value.reduce((total, fee) => total + (Number(fee.amount) || 0), 0)
);

const otherFeesTotal = computed(() =>
  otherFees.value.reduce((total, fee) => total + (Number(fee.price) || 0), 0)
);

// Combined terms for calculations and UI
const allLrfTerms = computed(() => {
  return [...defaultLrfTerms, ...customLrfTerms.value].sort((a, b) => a - b);
});

// Lease Rate Factors calculations based on Excel formulas (fixed per $1,000)
const leaseRateFactorsTable = computed(() => {
  const dealVal = dealValue; // Fixed at 1000
  const yield_ = yieldRate.value ? yieldRate.value / 100 : 0;
  const cof = cofRate / 100; // Fixed COF
  const residual = residualRate.value ? residualRate.value / 100 : 0;
  
  return allLrfTerms.value.map(termMonths => {
    // Calculate LRF per $1,000 (independent of deal value)
    const quarterlyLRF = pmt(yield_ / 4, termMonths / 3, -1000, 1000 * residual, 1);
    const monthlyLRF = pmt(yield_ / 12, termMonths, -1000, 1000 * residual, 1);
    
    // Calculate actual payments for the deal value
    const pmtQuarterly = quarterlyLRF * (dealVal / 1000);
    const pmtMonthly = monthlyLRF * (dealVal / 1000);
    
    // Calculate PV and Origination Fee using actual deal value
    const pvAtCOFQuarterly = pv(cof / 4, termMonths / 3, pmtQuarterly, dealVal * residual, 1) * -1;
    const pvAtCOFMonthly = pv(cof / 12, termMonths, pmtMonthly, dealVal * residual, 1) * -1;
    
    const originationFeeQuarterly = pvAtCOFQuarterly - dealVal;
    const originationFeeMonthly = pvAtCOFMonthly - dealVal;
    
    return {
      term: termMonths,
      quarterlyLRF: quarterlyLRF,
      monthlyLRF: monthlyLRF,
      pmtQuarterly: pmtQuarterly,
      pmtMonthly: pmtMonthly,
      pvAtCOFQuarterly: pvAtCOFQuarterly,
      pvAtCOFMonthly: pvAtCOFMonthly,
      originationFeeQuarterly: originationFeeQuarterly,
      originationFeeMonthly: originationFeeMonthly
    };
  });
});

// Excel PMT function implementation
const pmt = (rate, nper, pv, fv = 0, type = 0) => {
  if (rate === 0) return -(pv + fv) / nper;
  const pvif = Math.pow(1 + rate, nper);
  return -(pv * pvif + fv) / ((pvif - 1) / rate) / (1 + rate * type);
};

// Excel PV function implementation
const pv = (rate, nper, pmt, fv = 0, type = 0) => {
  if (rate === 0) return -pmt * nper - fv;
  const pvif = Math.pow(1 + rate, nper);
  return -(pmt * (1 + rate * type) * (pvif - 1) / rate + fv) / pvif;
};

// Computed properties for current calculations based on selected term
const currentQuarterlyLRF = computed(() => {
  const term = Number(selectedLrfTerm.value) || 36; // Default to 36 if unset
  const termData = leaseRateFactorsTable.value.find(t => t.term === term);
  return termData ? termData.quarterlyLRF : 0;
});

const currentMonthlyLRF = computed(() => {
  const term = Number(selectedLrfTerm.value) || 36; // Default to 36 if unset
  const termData = leaseRateFactorsTable.value.find(t => t.term === term);
  return termData ? termData.monthlyLRF : 0;
});

// Functions for managing custom months
const addCustomMonth = () => {
  const month = Number(newMonthInput.value);
  if (month && month > 0 && !allLrfTerms.value.includes(month)) {
    customLrfTerms.value.push(month);
    customLrfTerms.value.sort((a, b) => a - b);
    newMonthInput.value = null;
  }
};

const removeCustomMonth = (month) => {
  const index = customLrfTerms.value.indexOf(month);
  if (index > -1) {
    customLrfTerms.value.splice(index, 1);
    // If the removed month was selected, reset to default
    if (selectedLrfTerm.value === month) {
      selectedLrfTerm.value = defaultLrfTerms[0];
    }
  }
};

// Group approvals by fixed term length (months) for rendering multiple tables
const approvalsByTerm = computed(() => {
  const groups = {};
  const approvals = data.value.dsdTerritoryServiceRates.approvals || [];
  approvals.forEach(item => {
    const term = item.fixedTermInMonth;
    if (!groups[term]) groups[term] = [];
    groups[term].push({
      modelName: item.displayName,
      machineQuantity: (Number(item.dsdQuantity) || 0) + (Number(item.dealerQuantity) || 0),
      standardBWCpcRate: item.blackAndWhite,
      standardColourCpcRate: item.colour,
      extraLongBwRate: item.oversizePercentage,
      extraLongColourRate: item.iprc,
      minimumsApplicable: item.minimumBaseAmount || item.minimumBaseVolume ? 'YES' : 'NO',
      accessoryFeesIncludedInCPC: item.cpcIncludesAccessory === 'Y' ? 'YES' : 'NO',
      baseAmount: item.minimumBaseAmount,
      baseColourVolume: item.minimumBaseVolume
    });
  });
  return groups;
});



// --- UTILITY FUNCTIONS ---

const formatCurrency = (value) => {
  if (typeof value !== 'number') return '$0.00';
  return value.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
};

const formatSmallCurrency = (value) => {
    if (typeof value !== 'number') return '$0.0000';
    return value.toLocaleString('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 5 });
};

const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-CA'); // e.g., 2024-01-15
};

const calculatePercentage = (part, whole) => {
  if (typeof part !== 'number' || typeof whole !== 'number' || whole === 0) {
    return '0.00'; // Return as string to match .toFixed() output
  }
  return ((part / whole) * 100).toFixed(2);
};
// --- EVENT HANDLERS ---

const onApprove = async () => {
  const isDateValid = Boolean(priceValidityDate.value && isValidDate(priceValidityDate.value));
  const hasCategory = selectedCategory.value !== null && selectedCategory.value !== undefined && selectedCategory.value !== '';
  if (!(isDateValid && hasCategory)) {
    const parts = [];
    if (!isDateValid) parts.push('a valid Price Validity date (YYYY-MM-DD)');
    if (!hasCategory) parts.push('a Category selection');
    snackbar.value = { show: true, message: `Please provide ${parts.join(' and ')}.`, color: 'warning' };
    return;
  }
  isSubmitting.value = true;
  try {
    const payload = {
      action : "approve",
      requestId: props.id,
      category: selectedCategory.value,
      issueDate: new Date().toISOString(),
      priceValidityDate: new Date(priceValidityDate.value).toISOString(),
      quarterlyLRF: Number(currentQuarterlyLRF.value.toFixed(2)),
      monthlyLRF: Number(currentMonthlyLRF.value.toFixed(2)),
      priceDeskRemark: priceDeskComments.value,
      serviceApprovalRemark: serviceComments.value,
      leaseRateRemark: leaseRateFactorComments.value,
      additionalCharges: [
        ...(Array.isArray(data.value.additionalCharges) ? data.value.additionalCharges : []),
        ...(Array.isArray(otherFees.value) ? otherFees.value : [])
      ],
      approvedSoftCosts: selectedModels.value
        .filter(model => model.value && softCostRates.value[model.value])
        .map(model => {
          // Start with original soft cost rates
          const baseRates = { ...softCostRates.value[model.value] };
          
          // Apply any edited values from editedSoftCostPrices
          if (editedSoftCostPrices.value[model.value]) {
            // For each cost item that has been edited
            data.value.softCosts.costItems.forEach(item => {
              if (editedSoftCostPrices.value[model.value][item.id] !== undefined) {
                // Map the cost item ID to the appropriate field in the payload
                const rawDesc = String(item?.description || '').split('(')[0].trim().toLowerCase();
                const mapping = {
                  'delivery': 'deliveryNote1',
                  'pick up': 'pickupNote2',
                  'training': 'trainingNote3',
                  'equipment installation': 'equipmentInstallationNote4',
                  'software installation': 'softwareInstallationNote5',
                  'environmental fee': 'environmentFee',
                };
                
                const key = mapping[rawDesc];
                if (key) {
                  // Apply the edited value to the appropriate field
                  baseRates[key] = Number(editedSoftCostPrices.value[model.value][item.id]) ?? 0;
                }
              }
            });
          }
          
          return {
            modelId: model.value,
            supportTypeId: 20, // Default support type
            modelCode: model.text?.split(' - ')[0] || '',
            modelName: model.text || '',
            isActive: 'Y',
            ...baseRates
          };
        })
    };
    await postApprovedPricing(props.id, payload);
    snackbar.value = { show: true, message: 'Pricing approved successfully.', color: 'success' };
    router.push({ name: 'pagePriceDesk' });
  } catch (error) {
    console.error('Failed to approve pricing:', error);
    snackbar.value = { show: true, message: 'Failed to approve pricing. Please try again.', color: 'error' };
  } finally {
    isSubmitting.value = false;
  }
};
const exportQuote = () => console.log('Exporting to Excel');

// --- DYNAMIC UI LOGIC ---

// Additional Service Fees
const addAdditionalServiceFee = () => additionalServiceFees.value.push({ description: '', amount: 0 });
const removeAdditionalServiceFee = (index) => additionalServiceFees.value.splice(index, 1);
// No-op handler to trigger reactivity when editing fee amounts directly
const updateAdditionalServiceFeesTotal = () => { /* computed prop recalculates automatically */ };

// Other Fees
const addOtherFee = () => {
  otherFees.value.push({ unitName: '', price: 0,isActive: 'Y' });
};

const removeOtherFee = (index) => {
  otherFees.value.splice(index, 1);
  // The computed property 'otherFeesTotal' will update automatically
};

const updateOtherFeesTotal = () => {
  // This function is intentionally left empty. 
  // It's called by the template to ensure reactivity when a fee amount is changed directly.
  // The 'otherFeesTotal' computed property handles the actual calculation.
};



const getAvailableModels = (colIndex) => {
  const selectedValues = selectedModels.value.map(m => m.value).filter((v, i) => i !== colIndex && v !== null);
  return modelOptions.value.filter(option => !selectedValues.includes(option.value));
};

const addNewColumn = () => {
  if (selectedModels.value.length < modelOptions.value.length) {
    selectedModels.value.push({ value: null, label: 'Select Item' });
  }
};

// Watch selected models and fetch soft cost rates when a new model selected
watch(selectedModels, async (newArr) => {
  for (const sel of newArr) {
    if (sel.value && !softCostRates.value[sel.value]) {
      try {
        const { data: scData } = await getSoftCostRates(sel.value);
        softCostRates.value[sel.value] = scData;
      } catch (err) {
        console.error('Failed to fetch soft cost', err);
      }
    }
  }
}, { deep: true });

const addNewColumnIfNeeded = () => {
  if (selectedModels.value.every(m => m.value !== null) && selectedModels.value.length < modelOptions.value.length) {
    addNewColumn();
  }
};

const updateSoftCostPrice = (modelValue, costItemId, newValue) => {
  // Initialize nested structure if needed
  if (!editedSoftCostPrices.value[modelValue]) {
    editedSoftCostPrices.value[modelValue] = {};
  }
  
  // Store the edited value
  editedSoftCostPrices.value[modelValue][costItemId] = newValue;
};

const getDisplayValueForSoftCost = (item, modelValue) => {
  // Return edited value if exists, otherwise return original
  if (editedSoftCostPrices.value[modelValue] && 
      editedSoftCostPrices.value[modelValue][item.id] !== undefined) {
    return editedSoftCostPrices.value[modelValue][item.id];
  }
  return getCostForModel(item, modelValue);
};

const getCostForModel = (item, modelValue) => {
  if (!modelValue) return null;
  
  // Check if we have API response data (flat structure)
  const rateData = softCostRates.value[modelValue];
  if (rateData) {
    // Normalize description (strip note suffixes like "(Note 1)", trim, and lowercase)
    const rawDesc = String(item?.description || '').split('(')[0].trim().toLowerCase();

    const mapping = {
      'delivery': 'deliveryNote1',
      'pick up': 'pickupNote2', 
      'training': 'trainingNote3',
      'equipment installation': 'equipmentInstallationNote4',
      'software installation': 'softwareInstallationNote5',
      'environmental fee': 'environmentFee',
    };

    const key = mapping[rawDesc];
    const value = key ? rateData[key] : null;
    return value !== null && value !== undefined ? Number(value) : null;
  }
  
  // Fallback to dummy data structure (nested costs object)
  if (item?.costs && item.costs[modelValue]) {
    return Number(item.costs[modelValue]);
  }
  
  return null;
};

const handleCategoryChange = async (categoryId) => {
  if (!categoryId) return;
  try {
    const response = await getCategoryDetails(categoryId);
    const categoryDetails = response.data; 
    if (categoryDetails && categoryDetails.minCommitment) {
      data.value.hardwarePriceProgramTerms.minimumCommitment = categoryDetails.minCommitment;
    }
  } catch (error) {
    console.error('Failed to fetch category details:', error);
  }
};


// --- DATA TRANSFORMATION ---

const mapApiResponseToData = (apiResponse) => {
  const { items, customer, priceValidityDate, issueDate } = apiResponse;

  // Determine best customer display name based on available fields
  const customerDisplayName = customer?.displayName || customer?.businessName || customer?.legalName || customer?.customerName || 'Unknown Customer';

  const documentHeader = {
    quoteNumber: 'DSD-Q-24-000XXX', // Placeholder
    quoteDate: issueDate,
    priceValidity: priceValidityDate,
    customerName: customerDisplayName,
    // Build address only if parts are present
    customerAddress: customer?.addressLine1 ? `${customer.addressLine1}${customer.city ? ', ' + customer.city : ''}${customer.province ? ', ' + customer.province : ''}${customer.postalCode ? ', ' + customer.postalCode : ''}` : '',
    contactName: customer?.contactName || '',
    contactEmail: customer?.contactEmail || '',
    contactPhone: customer?.contactPhone || '',
    region: customer?.region || '',
    customerType: customer?.customerType || '',
  };

  const productGroups = [];
  const softwareLicensing = [];
  const mainItems = items.filter(i => i.parentItemId === null);
  const subItems = items.filter(i => i.parentItemId !== null);

  mainItems.forEach(mainItem => {
    const group = {
      id: mainItem.itemId,
      groupName: mainItem.modelName,
      mainItem: {
        productDescription: mainItem.displayName,
        itemNumber: mainItem.itemNumber,
        dsdQty: mainItem.dsdQuantity,
        dealerTerritoryQty: mainItem.dealerIt,
        totalQty: mainItem.dsdQuantity + mainItem.dealerIt,
        msrp: mainItem.msrp,
        approvedSellingPrice: mainItem.requestedSellingPricePL,
        approvedSellingPricePercentMSRP: calculatePercentage(mainItem.requestedSellingPricePL, mainItem.msrp),
      },
      accessories: subItems
        .filter(sub => sub.parentItemId === mainItem.itemId)
        .map(acc => ({
          productDescription: acc.displayName,
          itemNumber: acc.itemNumber,
          dsdQty: acc.dsdQuantity,
          dealerTerritoryQty: acc.dealerIt,
          totalQty: acc.dsdQuantity + acc.dealerIt,
          msrp: acc.msrp,
          approvedSellingPrice: acc.requestedSellingPricePL,
          approvedSellingPricePercentMSRP: calculatePercentage(acc.requestedSellingPricePL, acc.msrp),
        })),
    };

    const allItemsInGroup = [group.mainItem, ...group.accessories];
    const groupTotal = {
        msrp: allItemsInGroup.reduce((sum, item) => sum + (item.msrp * item.totalQty), 0),
        approvedSellingPrice: allItemsInGroup.reduce((sum, item) => sum + ((item.approvedSellingPrice) * item.totalQty), 0),
        approvedSellingPricePercentMSRP: 0 // Will be calculated next
    };
    groupTotal.approvedSellingPricePercentMSRP = calculatePercentage(groupTotal.approvedSellingPrice, groupTotal.msrp);

    if (mainItem.isSolution === 'Y') {
      softwareLicensing.push({
        id: group.id,
        category: group.groupName,
        items: allItemsInGroup,
        categoryTotal: groupTotal
      });
    } else if (mainItem.isMainframe === 'Y') {
      productGroups.push({ ...group, groupTotal });
    }
  });

  const allProductItems = productGroups.flatMap(g => [g.mainItem, ...g.accessories]);
  const allSoftwareItems = softwareLicensing.flatMap(c => c.items);
  const summaryTotals = {
    totalMSRP: [...allProductItems, ...allSoftwareItems].reduce((sum, item) => sum + (item.msrp * item.totalQty), 0),
    totalApprovedPricing: [...allProductItems, ...allSoftwareItems].reduce((sum, item) => sum + (item.approvedSellingPrice * item.totalQty), 0),
  };

  // Map remarks from API response to reactive refs for UI binding
  priceDeskComments.value = apiResponse?.priceDeskRemark || '';
  serviceComments.value = apiResponse?.serviceApprovalRemark || '';
  leaseRateFactorComments.value = apiResponse?.leaseRateRemark || '';
      quarterlyLRF.value = apiResponse?.quarterlyLRF || '';
      // priceValidityDate.value = apiResponse?.priceValidityDate || '';
      monthlyLRF.value = apiResponse?.monthlyLRF || '';

      // Map LRF input parameters from API if available (dealValue and cofRate are now fixed)
      yieldRate.value = apiResponse?.yieldRate || 11.00;
      residualRate.value = apiResponse?.residualRate || 0;
      
      // Load custom terms if available from API
      if (apiResponse?.customLrfTerms && Array.isArray(apiResponse.customLrfTerms)) {
        customLrfTerms.value = apiResponse.customLrfTerms.filter(term => !defaultLrfTerms.includes(term));
      }

  // Merge required static sections from dummy data that the API doesn't provide
  return {
    ...dummyQuoteData,

    documentHeader,
    summaryTotals,
    productGroups,
    softwareLicensing,
    // Preserve other sections with default empty structure
    hardwarePriceProgramTerms: {
      ...dummyQuoteData.hardwarePriceProgramTerms,
      minimumCommitment: data.value.hardwarePriceProgramTerms.minimumCommitment,
    },
    dsdTerritoryServiceRates: { ...data.value.dsdTerritoryServiceRates,serviceValuePack: apiResponse.serviceValuePack, approvals: apiResponse.approvals || [] },
    colourModelTermsAndConditions: data.value.colourModelTermsAndConditions,
    generalServiceRelatedTermsAndConditions: data.value.generalServiceRelatedTermsAndConditions,
    leaseRateFactors: data.value.leaseRateFactors,
    softCosts: data.value.softCosts,
    additionalCharges: apiResponse.additionalCharges || [],
    footerNotes: dummyQuoteData.footerNotes,
  };
};

// --- LIFECYCLE HOOKS ---

// Watch for data changes to expand all panels by default
watch(() => data.value?.productGroups, (newGroups) => {
  if (newGroups && newGroups.length) {
    // Initialize with all panel indices (0 to length-1)
    expandedProductPanels.value = Array.from({ length: newGroups.length }, (_, i) => i);
  }
}, { immediate: true });

watch(() => data.value?.softwareLicensing, (newCategories) => {
  if (newCategories && newCategories.length) {
    // Initialize with all panel indices (0 to length-1)
    expandedSoftwarePanels.value = Array.from({ length: newCategories.length }, (_, i) => i);
  }
}, { immediate: true });

watch(activeTab, async (newVal) => {
  if (newVal === 'requestDetails' && previewTabRef?.value?.refreshData) {
    await previewTabRef.value.refreshData();
  }
});

onMounted(async () => {
  try {
    categoryOptions.value = await getLov(LovCategories.CATEGORY_VALUE_PACK);
  } catch (error) {
    console.error('Failed to load category options:', error);
  }

  // Populate model dropdown options from LOV
  try {
    const lovData = await getLov(LovCategories.MODEL_CATEGORY, false);
    modelOptions.value = lovData.map((item) => ({ label: item.value || item.description, value: item.lookupCode || item.code }));

  } catch (e) {
    console.error('Failed to load MODEL_CATEGORY LOV', e);
  }

  const requestId = route.params.id;
  if (requestId) {
    try {
      const response = await getApprovedPricing(requestId);
      data.value = mapApiResponseToData(response.data);
      // Seed price validity date from API response if available
      const apiDate = response.data?.priceValidityDate || data.value?.documentHeader?.priceValidity;
      if (apiDate) {
        try {
          priceValidityDate.value = new Date(apiDate).toISOString().slice(0, 10);
        } catch (e) {
          // ignore parse errors
        }
      }

      // Pre-select category from API response if present
      const apiCategoryRaw = response.data?.category || response.data?.documentHeader?.category;
      if (apiCategoryRaw) {
        const byCode = Array.isArray(categoryOptions.value) ? categoryOptions.value.find(opt => opt.lookupCode === apiCategoryRaw) : null;
        const byDesc = Array.isArray(categoryOptions.value) ? categoryOptions.value.find(opt => opt.description === apiCategoryRaw || opt.value === apiCategoryRaw) : null;
        const matched = byCode || byDesc;
        if (matched?.lookupCode) {
          selectedCategory.value = matched.lookupCode;
          await handleCategoryChange(selectedCategory.value);
        }
      }

      // Populate model options for Soft Costs section from product groups
      // const models = data.value.productGroups.map(g => ({ 
      //   label: g.groupName, 
      //   value: g.id 
      // }));
      // modelOptions.value = models;
      appStore.stopPageLoader();
    } catch (error) {
      console.error('Failed to fetch approved pricing:', error);
      appStore.stopPageLoader();
      // Future: Show a user-friendly notification
    }
  }
});

</script>

<style scoped>
/* Add any specific styles here if needed, but try to use Vuetify classes first */
.text-caption {
  color: rgba(0,0,0,0.6);
}
/* Ensure section-card has a class or use a more specific selector if SectionCard.vue doesn't have global styles */
.v-card.my-4 { /* Targeting SectionCard via its root element and class */
  margin-bottom: 24px !important; /* Overriding Vuetify's default if needed, or use more specific classes */
}
/* Right-align numeric inputs used in fee tables */
.fee-input :deep(input) {
  text-align: right;
}
</style>
