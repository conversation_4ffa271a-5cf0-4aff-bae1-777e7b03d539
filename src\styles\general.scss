/**
 * @file Primary SCSS file with styles that will apply site wide.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

// Creates a top border (box shadow) on the element with the error colour.
.box-shadow-top-error { box-shadow : inset 0 -1px 0 rgb(var(--v-theme-border)), inset 0 8px 0 rgb(var(--v-theme-error)) !important; }
.box-shadow-top-success { box-shadow : inset 0 -1px 0 rgb(var(--v-theme-border)), inset 0 8px 0 rgb(var(--v-theme-success)) !important; }
.box-shadow-top-warning { box-shadow : inset 0 -1px 0 rgb(var(--v-theme-border)), inset 0 8px 0 rgb(var(--v-theme-warning)) !important; }
.box-shadow-top-info { box-shadow : inset 0 -1px 0 rgb(var(--v-theme-border)), inset 0 8px 0 rgb(var(--v-theme-info)) !important; }

// Sets theme for custom card (1).
.app-message-card
{
    .v-card-header
    {
        background-color : rgb(var(--v-theme-background-1)) !important;
        color            : rgb(var(--v-theme-on-background-1)) !important;
        box-shadow       : inset 0 -1px 0 rgb(var(--v-theme-border)), inset 0 8px 0 rgb(var(--v-theme-secondary));
        padding          : 12px 24px 4px !important;
    }

    .v-card-actions
    {
        padding : 0px 24px 14px !important;
    }
}

// Overrides border colour of cards when outlined.
.v-card--variant-outlined
{
    border : thin solid rgb(var(--v-theme-border)) !important;
}

.viewport-centre
{
    max-height: calc(100% - 82px);

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.no-link
{
    text-decoration: none;
    color: inherit;
}

// Make text color black for all form fields across the entire application
.v-field__input,
.v-select__selection,
.v-text-field input,
.v-textarea textarea {
    color: #000000 !important;
}

// Ensure text remains black when disabled
.v-field--disabled .v-field__input,
.v-field--disabled .v-select__selection,
.v-text-field--disabled input,
.v-textarea--disabled textarea {
    color: #000000 !important;
    opacity: 1 !important;
}

// Ensure text remains black when readonly
.v-field--readonly .v-field__input,
.v-field--readonly .v-select__selection,
.v-text-field--readonly input,
.v-textarea--readonly textarea {
    color: #000000 !important;
}


// Ensure labels remain black when disabled or readonly
.v-field--disabled .v-label,
.v-field--readonly .v-label {
    color: #000000 !important;
    opacity: 1 !important;
}


// Ensure placeholder text is visible but distinguishable
.v-field__input::placeholder {
    color: #757575 !important;
    opacity: 1 !important;
}

/* Canon tabs visual: selected prominent (red), unselected subtle (white) */
.canon-tabs .v-tab {
  background-color: white !important;
}

.canon-tabs .v-tab .v-btn__content,
.canon-tabs .v-tab .v-tab__content {
  color: rgb(var(--v-theme-canon)) !important;
}

.canon-tabs .v-tab--selected {
  background-color: rgb(var(--v-theme-canon)) !important;
}

.canon-tabs .v-tab--selected .v-btn__content,
.canon-tabs .v-tab--selected .v-tab__content {
  color: white !important;
}

/* Disabled tabs: keep white bg, muted text, and no global opacity blur */
.canon-tabs .v-tab.v-btn--disabled {
  background-color: #f5f5f5 !important;
  opacity: 1 !important;
}

.canon-tabs .v-tab.v-btn--disabled .v-btn__content,
.canon-tabs .v-tab.v-btn--disabled .v-tab__content {
  color: rgba(0, 0, 0, 0.38) !important;
}