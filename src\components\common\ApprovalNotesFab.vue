<template>
  <div class="approval-notes-fab">
    <!-- Floating Action Button -->
    <v-btn
      fab
      color="primary"
      size="large"
      elevation="6"
      class="fab-button"
      @click="togglePanel"
      :title="isOpen ? 'Close Approval Notes' : 'Open Approval Notes'"
    >
      <v-icon size="large">
        {{ isOpen ? 'mdi-close' : 'mdi-note-text-outline' }}
      </v-icon>
    </v-btn>

    <!-- Approval Notes Panel -->
    <ApprovalNotesPanel v-model="isOpen" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ApprovalNotesPanel from './ApprovalNotesPanel.vue';

// Local state
const isOpen = ref(false);

// Methods
const togglePanel = () => {
  isOpen.value = !isOpen.value;
};
</script>

<style scoped>
.approval-notes-fab {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.fab-button {
  transition: all 0.3s ease;
}

.fab-button:hover {
  transform: scale(1.1);
}

/* Ensure FAB is above other content */
.approval-notes-fab :deep(.v-btn) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.approval-notes-fab :deep(.v-btn:hover) {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}
</style>
