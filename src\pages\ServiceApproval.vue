<template>
    <v-tabs v-model="activeTab" grow class="mb-4 canon-tabs"  bg-color="canon">
      <v-tab value="serviceForm">Service Form</v-tab>
      <v-tab value="requestDetails">Request Details</v-tab>
    </v-tabs>
        <v-window v-model="activeTab" class="pnl-window">
<v-window-item value="serviceForm">
  <v-container fluid>
    <v-row>
      <v-col cols="12" md="6">
        <v-card class="mb-4">
          <v-card-title class="text-subtitle-1 font-weight-bold">Current Usage Information</v-card-title>
          <v-card-text>
            <v-textarea
              v-model="currentUsageInformation"
              readonly
              auto-grow
              rows="3"
              variant="outlined"
              :placeholder="currentUsageInformation ? '' : 'No information'"
            />
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" md="6">
        <v-card class="mb-4">
          <v-card-title class="text-subtitle-1 font-weight-bold">Service Business Case</v-card-title>
          <v-card-text>
            <v-textarea
              v-model="serviceBusinessCase"
              readonly
              auto-grow
              rows="3"
              variant="outlined"
              :placeholder="serviceBusinessCase ? '' : 'No information'"
            />
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <service-approval-table
      :term-length="termLength"
      :service-value-pack="serviceValuePack"
      :service-levels="serviceLevels"
      :sections-data="tableSections"
      :customer-name="customerName"
      :view-mode="isViewMode"
      @update:term-length="termLength = $event"
      @update:service-value-pack="serviceValuePack = $event"
      @submit="handleSubmit"
    />
  </v-container>
  </v-window-item>
      <!-- Request Details Tab Content -->
      <v-window-item value="requestDetails">
        <PreviewTab ref="previewTabRef" :request-id="props.id" />
      </v-window-item>
        </v-window>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import ServiceApprovalTable from '@/components/service/ServiceApprovalTable.vue';
import { getServiceApprovalOptions, postServiceApprovalOptions, type DsdRequestServiceApprovalOptionInput } from '@/lib/api';
import { getLov, LovCategories, type LovItem } from '@/services/lovService';
import { useAppStore } from '@/stores/AppStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import { DsdRequestStatus } from '@/enums/DsdRequestStatus';
import { useRouter } from 'vue-router';
import PreviewTab from '@/components/sales/PreviewTab.vue';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});
const activeTab = ref<'serviceForm' | 'requestDetails'>('serviceForm');
const previewTabRef = ref<InstanceType<typeof PreviewTab> | null>(null);

const router = useRouter();
// Sample data - replace with actual data fetching
const termLength = ref(5);
const serviceValuePack = ref<string>('');
const serviceLevels = ref<{ label: string; value: string }[]>([]);
const customerName = ref<string>('');
const currentUsageInformation = ref<string>('');
const serviceBusinessCase = ref<string>('');
const isViewMode = ref<boolean>(false);

const tableSections = ref<any[]>([]);
  const appStore = useAppStore();
  const snackbarStore = useSnackbarStore();
onMounted(async () => {
  // Fetch dropdown values for Service Value Pack
  try {
    const lovData = await getLov(LovCategories.SERVICE_VALUE_PACK, false);
    serviceLevels.value = lovData.map((item) => ({ label: item.value, value: item.lookupCode }));
  } catch (err) {
    console.error('Failed to load Service Value Pack LOV:', err);
  }

  try {
    const response = await getServiceApprovalOptions(props.id);
    const apiResponse = response.data;

    // Map new API response
    // Capture service pack id and customer name for header and Excel filename
    if (apiResponse?.servicePackId !== undefined && apiResponse?.servicePackId !== null) {
      serviceValuePack.value = String(apiResponse.servicePackId);
    }
    customerName.value = apiResponse?.customer?.displayName || apiResponse?.customer?.businessName || '';

    // Set view-only fields and mode
    currentUsageInformation.value = apiResponse?.currentUsageInformation || '';
    serviceBusinessCase.value = apiResponse?.serviceBusinessCase || '';
    isViewMode.value = apiResponse?.serviceFormStatus === DsdRequestStatus.STATUS_SERVICE_FORM_APPROVED;

    const apiData = (apiResponse?.serviceApprovalOptions ?? []);

    // Group API data by itemId to handle multiple term options for the same product
    const itemsById = apiData.reduce((acc: Record<string, any[]>, item: any) => {
      if (!acc[item.itemId]) {
        acc[item.itemId] = [];
      }
      acc[item.itemId].push(item);
      return acc;
    }, {} as Record<string, any[]>);

    // Split models into B&W vs Color sections based on API values
    const bwItems: any[] = [];
    const colorItems: any[] = [];
    Object.values(itemsById).forEach((itemGroup: any) => {
      const firstItem = itemGroup[0]; // Product details are the same across the group

      const serviceOptions = itemGroup.map((option: any) => {
        const rates = {
          "B&W": option.blackAndWhite,
          "Colour": option.colour,
          "Extra Long (iPRC only)": option.iprc,
          "Base $Amt": option.minimumBaseAmount,
          "Base Volume": option.minimumBaseVolume,
        };

        return {
          termLength: `${option.fixedTermInMonth} Months`,
          Requested: isViewMode.value ? {} : rates,
          Approved: isViewMode.value ? rates : {
            "B&W": null,
            "Colour": null,
            "Extra Long (iPRC only)": null,
            "Base $Amt": null,
            "Base Volume": null,
          },
          serviceApprovalId: option.serviceApprovalId,
          serviceFormId: apiResponse?.serviceFormId,
        };
      });

      const modelObj = {
        "Model": firstItem.displayName,
        "Qty": firstItem.dsdQuantity, // Or dealerQuantity?
        "Estimated AMV / Unit": firstItem.amvUnit,
        "Colour %": firstItem.colourPercentage,
        "Oversize %": firstItem.oversizePercentage,
        "Inclusive Plan Yes/No": firstItem.isDealerAcceptedSr ?? 'N',
        serviceOptions: serviceOptions,
      };

      // If for this model all options have both Colour and IPRC null/undefined, classify as B&W
      const isBw = itemGroup.every((option: any) =>
        (option.colour === null || option.colour === undefined) &&
        (option.iprc === null || option.iprc === undefined)
      );
      if (isBw) {
        bwItems.push(modelObj);
      } else {
        colorItems.push(modelObj);
      }
    });

    tableSections.value = [
      {
        section_name: "B&W",
        data_rows: bwItems
      },
      {
        section_name: "Color",
        data_rows: colorItems
      }
    ];
    appStore.stopPageLoader();
  } catch (error) {
    console.error('Failed to load service approval options:', error);
    appStore.stopPageLoader();
  }
});

// Helper to sanitize numeric fields coming from table (replace '-' or empty with null and ensure number)
const sanitize = (val: any, key: string = '') => {
  if (val === '-' || val === '' || val === undefined || val === null) return null;
  if (key === 'Extra Long (iPRC only)' && typeof val === 'string') {
    return val; // Return string as-is for IPRC
  }
  return typeof val === 'string' ? parseFloat(val) : val;
};

watch(activeTab, async (newVal) => {
  if (newVal === 'requestDetails' && previewTabRef?.value?.refreshData) {
    await previewTabRef.value.refreshData();
  }
});

const handleSubmit = async (approvedData: any) => {
  try {
    // The approvedData from the event is expected to be the list of items.
    // We need to transform it to the POST API payload format matching DsdRequestServiceApprovalOptionInupt structure.
    const approvalInputs = approvedData.flatMap((section: any) => 
        section.data_rows.flatMap((row: any) => 
            row.serviceOptions.map((option: any) => ({
                serviceApprovalId: option.serviceApprovalId,
                serviceFormId: option.serviceFormId,
                blackAndWhite: sanitize(option.Approved['B&W']),
                colour: sanitize(option.Approved['Colour']),
                iprc: sanitize(option.Approved['Extra Long (iPRC only)'], 'Extra Long (iPRC only)'),
                minimumBaseAmount: sanitize(option.Approved['Base $Amt']),
                minimumBaseVolume: sanitize(option.Approved['Base Volume']),
                userRemark: "Updated from approval screen" // Or get from a field
            }))
        )
    );

    // Extract serviceFormId and servicePackId from the first approval input
    const firstApprovalInput = approvalInputs[0];
    if (!firstApprovalInput) {
      throw new Error('No approval data to submit');
    }

    const payload: DsdRequestServiceApprovalOptionInput = {
      serviceFormId: firstApprovalInput.serviceFormId,
      servicePackId: parseInt(serviceValuePack.value) || null,
      approvalInputs: approvalInputs,
      action : "approve"
    };

    await postServiceApprovalOptions(payload);
    snackbarStore.show({
      text: 'Approval submitted successfully',
      color: 'success',
      timeout: 3000,
    });
    appStore.stopPageLoader();
    router.push({ name: 'pageServiceDesk' });
    // Optionally, navigate away or show a success message
  } catch (error) {
    console.error('Error submitting approval:', error);
    appStore.stopPageLoader();
    snackbarStore.show({
      text: 'Error submitting approval',
      color: 'error',
      timeout: 3000,
    });
  }
};
</script>

<style scoped>
/* Add any page-specific styles here */
</style>
