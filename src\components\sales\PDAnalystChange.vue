<template>
    <v-dialog v-model="dialogModel" max-width="500px">
      <v-card>
        <v-card-title>Assign/Reassign PD Analyst</v-card-title>
<v-card-text>
  <AutoCompleteManagerSEarch
    v-model="selectedPDAnalyst"
    :label="'PD Analyst'"
    :employees="pdAnalysts"
    :loading="loadingPDAnalyst"
    :error-messages="errorMessage"
    :search="pdAnalystSearch"
    @update:search="onPDAnalystSearch"
  />
</v-card-text>

        <v-card-actions class="justify-end">
          <v-btn @click="confirmSelection" color="primary" :disabled="!selectedPDAnalyst">{{ t('done') }}</v-btn>
          <v-btn @click="closeDialog" color="error">{{ t('cancel') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </template>
  
  <script setup lang="ts">
import { CanonAuth } from '@/lib/canonAuth';
import { CanonAuthState, Channel_Action, type Employee } from '@/lib/common/types';
  import { authFail } from '@/composables/auth/setAuthState';

import { getSearchedUserReportes, getUserReportes } from '@/lib/msGraph';
import { useAppStore } from '@/stores/AppStore';
import { useUserStore } from '@/stores/UserStore';
import { debounce } from 'lodash';
import { ref, computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { getSalesRequest, saveSalesRequest, updateSalesApprovers } from '@/services/salesRequestService';
import AutoCompleteManagerSEarch from './AutoCompleteManagerSEarch.vue';
import { getuserListByRole } from '@/lib/api';
import { DsdRequestStatus } from '@/enums/DsdRequestStatus';
import { useSnackbarStore } from '@/stores/SnackbarStore';
  
  const { t } = useI18n();
  const appStore = useAppStore();

// Props
const props = defineProps({
  modelValue: Boolean,
  itemId: Number,
});
const snackbarStore = useSnackbarStore();

  const pdAnalysts = ref<Employee[]>([]);
    const errorMessage = ref('');
  
const loadingPDAnalyst = ref(false)
const pdAnalystSearch = ref('')
  const emit = defineEmits(['update:modelValue', 'confirm']);
  
  const selectedManager = ref<Employee | null>(null);

  const dialogModel = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });
  onMounted(() => {
    pdAnalysts.value=[]
});
const selectedPDAnalyst = ref<Employee | null>(null);

// Fetch sales request details
const fetchSalesRequestDetails = async () => {
  if (!props.itemId) return;
  try {
    const response = await getSalesRequest(props.itemId);
    // Set current PD Analyst if exists
    if (response.data.pdAssignmentUserId && response.data.pdAssignmentUserName) {
      selectedPDAnalyst.value = {
        id: response.data.pdAssignmentUserId,
        displayName: response.data.pdAssignmentUserName,
        mail:'',
        mailNickname:  '',
        jobTitle: '',
        mobilePhone: '',
        department:'',
        employeeId: '',
        office:  '',
      };
    }
  } catch (error) {
    console.error('Failed to fetch sales request details:', error);
  }
};

// Watch for dialog opening
watch(() => dialogModel.value, (newVal) => {
  if (newVal) fetchSalesRequestDetails();
});


  const confirmSelection = () => {
   handlePDAnalystChange({ pdAssignmentUserId: selectedPDAnalyst.value?.id });
  };
  const handlePDAnalystChange = async (data:any) => {
    try {
        appStore.startLoader('loadingRequests','Updating PD Analyst Assignment...');
            const response = await updateSalesApprovers((props.itemId !== undefined ? props.itemId.toString() : ''), data);
          snackbarStore.show({
        text: 'PD Analyst assigned successfully',
        color: 'info',
        icon: 'info',
        timeout: 3000
      });
          dialogModel.value = false;

        appStore.stopLoader('loadingRequests');
    } catch (error) {
        console.error('Failed to update PD Analyst assignment:', error);
             snackbarStore.show({
        text: 'Failed to assign PD Analyst',
         color: 'error',
        icon: 'alert',
        timeout: 3000
      });
    } finally {
        appStore.stopLoader('loadingRequests');
    }
};
  const closeDialog = () => {
    emit('update:modelValue', false);
  };
  
  // Fetch employees based on search query
const fetchEmployees = async (searchQuery: string): Promise<Employee[]> => {

  if (!searchQuery) {
    pdAnalysts.value = [];
    return []; // Preventing API call on selection and blank string
  }
  if (selectedManager.value?.displayName == searchQuery) {
    return []; // Preventing API call on selection and blank string
  }

  try {
    const response = await getuserListByRole(Channel_Action.PRICE_DESK_ANALYST, searchQuery)
    return response.data.map((user: any) => ({
      id: user.accountId,
      displayName: user.displayName,
      mail: user.email,

    }));
  } catch (error) {
    console.error('Error fetching PD Analysts:', error);
    return [];
  } finally {
    loadingPDAnalyst.value = false
  }
};


const onPDAnalystSearch = (val: string) => {
  pdAnalystSearch.value = val
  debouncedFetchPDAnalysts(val)
}
const debouncedFetchPDAnalysts = debounce(async (val: string) => {
  loadingPDAnalyst.value = true
  try {
    // If val is empty, do not clear the list; keep the last options
    if (val) pdAnalysts.value = await fetchEmployees(val)
  } finally {
    loadingPDAnalyst.value = false
  }
}, 300)

  </script>
  
