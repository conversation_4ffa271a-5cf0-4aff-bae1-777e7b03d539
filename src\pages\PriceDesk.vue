<script setup lang="ts">
/**
 * @file Price Desk page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { getPriceDeskList, type PriceDeskRequest } from '@/services/salesRequestService';
import DataTable from '@/components/common/DataTable.vue';
import StatusBadge from '@/components/core/StatusBadge.vue';
import { DsdRequestStatus } from '@/enums/DsdRequestStatus';
import { useAppStore } from '@/stores/AppStore';
import { useRouter } from 'vue-router';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import { exportApprovedPricingReportAPI } from '@/lib/api';
import actionPermissionChannel from '@/composables/auth/actionPermissionChannel';
import { Channel_Action, CN_Action } from '@/lib/common/types';
import actionPermissions from '@/composables/auth/actionPermission';
import PDAnalystChange from '@/components/sales/PDAnalystChange.vue';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();
const snackbarStore = useSnackbarStore();

// Add language support
const { t } = useI18n();
const router = useRouter();

// Table data
const loading = ref(false);
const selectedItemId = ref<number | null>(null);
const showPDAnalystSearchDialog = ref(false);
const headers = computed(() => [
    { title: t('page.price_desk.table.header.request_number'), key: 'requestNumber', sortable: true },
    { title: t('page.price_desk.table.header.customer'), key: 'customer.businessName', sortable: true },
    { title: t('page.price_desk.table.header.created_by'), key: 'createdByName', sortable: true },
    { title: t('page.price_desk.table.header.pd_analyst'), key: 'pdAssignmentName', sortable: true, align: 'center' },
    { title: t('page.price_desk.table.header.last_updated'), key: 'updatedAt', sortable: true },
    { title: t('page.price_desk.table.header.status'), key: 'dsdRequestStatus', sortable: true },
    { title: t('common.actions'), key: 'actions', sortable: false }
]);

const items = ref<PriceDeskRequest[]>([]);

// Handle table actions
const handleAction = async ({ action, item }: { action: string; item: any }) => {
    if (action === 'view') {
      router.push({ name: 'pageProfitLossCalculator', params: { id: item.requestId } });

      // Handle edit action
      console.log('Edit price:', item);
    } else if (action === 'approve_pricing') {
      // Handle approve pricing action
      router.push({ name: 'Quote', params: { id: item.requestId } });
      console.log('Approve pricing for request:', item);
    } else if (action === 'assignPDAnalyst') {
      // Handle PD Analyst assignment
      openPDAnalystDialog(item);
      console.log('Assign PD Analyst for request:', item);
    } else if (action === 'export_approved_pricing') {
      try {
        const response = await exportApprovedPricingReportAPI(item.requestId);
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');

        let filename = `${item.requestNumber}_ApprovedPricing.xlsx`;

        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        snackbarStore.show({
          text: 'Approved Pricing exported successfully',
          color: 'success',
          icon: 'download',
          timeout: 2000
        });
      } catch (e) {
        console.error('Error exporting Approved Pricing report:', e);
        snackbarStore.show({
          text: 'Failed to export Approved Pricing report',
          color: 'error',
          icon: 'alert',
          timeout: 2500
        });
      }
    }
};

const openPDAnalystDialog = (item: any) => {
  selectedItemId.value = item.requestId;
  showPDAnalystSearchDialog.value = true;
};

// Refresh the list whenever the PD Analyst dialog is closed
watch(showPDAnalystSearchDialog, (val) => {
  if (!val) {
    fetchData();
  }
});

// Load data
const fetchData = async () => {
  loading.value = true;
  try {
    items.value = await getPriceDeskList();
  } catch (error) {
    console.error('Failed to fetch price desk list:', error);
    // Optionally, show an error message to the user
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
    fetchData();
    // Stop page loader when component is mounted
    appStore.stopPageLoader();
});
</script>

<template>
    <div class="pa-4">
                <h1>{{ t('page.price_desk.title') }}</h1>
        
        <!-- <v-card class="mt-4"> -->
        
            
            <v-card-text>
                <DataTable
                    :headers="headers"
                    :items="items"
                    :loading="loading"
                    @action="handleAction"
                >
                    <template v-slot:item.updatedAt="{ item }">
                        {{ new Date(item.updatedAt).toLocaleDateString() }}
                    </template>

                    <template v-slot:item.pdAssignmentName="{ item }">
                        {{ item.pdAssignmentName ?? '-' }}
                    </template>

                    <template v-slot:item.dsdRequestStatus="{ item }">
                        <StatusBadge :status="item.dsdRequestStatus==DsdRequestStatus.PD_APPROVAL_PENDING && item.isPnlApproved=='Y' ? DsdRequestStatus.APPROVAL_PRICING_PENDING : item.dsdRequestStatus" />
                    </template>

                    <template v-slot:item.actions="{ item }">
                        <v-menu>
                            <template v-slot:activator="{ props }">
                                <v-btn
                                    icon="more_vert"
                                    variant="text"
                                    size="small"
                                    v-bind="props"
                                ></v-btn>
                            </template>
                            <v-list>
                                <v-list-item
                                    prepend-icon="visibility"
                                    :title="'View Profit & Loss'"
                                    @click="handleAction({ action: 'view', item })"
                                ></v-list-item>
                                <v-list-item v-if="item.dsdRequestStatus === DsdRequestStatus.PD_APPROVAL_PENDING"
                                    prepend-icon="assignment_ind"
                                    title="Assign/Reassign PD Analyst"
                                    @click="handleAction({ action: 'assignPDAnalyst', item })"
                                ></v-list-item>
                                <v-list-item
                                    v-if="item.dsdRequestStatus === DsdRequestStatus.PD_APPROVAL_PENDING && item.isPnlApproved === 'Y'"
                                    prepend-icon="attach_money"
                                    title="Approve Pricing"
                                    @click="handleAction({ action: 'approve_pricing', item })"
                                ></v-list-item> 
                                <v-list-item
                                    v-if="item.dsdRequestStatus === DsdRequestStatus.APPROVED"
                                    prepend-icon="download"
                                    title="Export Approved Pricing Excel"
                                    @click="handleAction({ action: 'export_approved_pricing', item })"
                                ></v-list-item>
                            </v-list>
                        </v-menu>
                    </template>
                </DataTable>
            </v-card-text>
        <!-- </v-card> -->
    </div>
    
    <!-- PD Analyst Assignment Dialog -->
    <PDAnalystChange
        v-model="showPDAnalystSearchDialog"
        :item-id="selectedItemId !== null ? selectedItemId : undefined"
    />
</template>
