<script setup lang="ts">
 import { reactive, ref, watch, defineEmits, defineProps, onMounted, computed } from 'vue';
 import { getCmacRegistration, saveCmacRegistration, type SaveCmacRegistrationPayload } from '@/services/salesRequestService';
 import { useSnackbarStore } from '@/stores/SnackbarStore';
 import PreviewTab from '@/components/sales/PreviewTab.vue';

// --- Data Definitions ---
const sbiOptions = [
  { category: 'S1', commitment: '$1 Million +', term: '12 months' },
  { category: 'S2', commitment: '$2 Million +', term: '24 months' },
  { category: 'S2Y', commitment: '$4.5 Million +', term: '24 months' },
];

const sasOptions = [
  { category: 'SA', code: 'SA', commitment: '$100,000+', term: 'Per Approval' },
  { category: 'GA', code: 'GA', commitment: 'no commitment', term: 'Per Approval' },
  { category: 'SH', code: 'SH', commitment: 'no commitment', term: 'Per Approval' },
  { category: 'CK', code: 'CK', commitment: '$100,000+', term: 'Per Approval' },
];

// Default term (in months) by category where applicable
const defaultTermByCategory: Record<string, number | null> = {
  S1: 12,
  S2: 24,
  S2Y: 24,
  SA: null,
  GA: null,
  SH: null,
  CK: null,
};

// --- Component Props and Emits ---
const emit = defineEmits(['update:modelValue']);
const props = defineProps({
  modelValue: {
    type: Object as () => any,
    required: true,
  },
  status: {
    type: Number,
    required: false,
    default: null,
  },
  requestId: {
    type: Number,
    required: false,
    default: null,
  }
});

// Fetch existing CMAC registration when component mounts
onMounted(async () => {
  if (props.requestId) {
    try {
      const { data } = await getCmacRegistration(props.requestId);
      if (data) {
        mapApiToLocal(data);
      }
    } catch (e) {
      console.error('Failed to load CMAC registration', e);
    }
  }
});

// Expose getter for parent component
function getFormData() {
  return JSON.parse(JSON.stringify(localForm));
}
const saving = ref(false);
 const snackbarStore = useSnackbarStore();
 const activeTab = ref<'form' | 'preview'>('form');

async function handleSubmit() {
  if (!props.requestId) {
    console.error('requestId prop missing');
    snackbarStore.show({ text: 'Missing request ID. Please save Sales Request first.', color: 'error', timeout: 3000 });
    return;
  }
  // Basic validation
  if (!isValid.value) {
    snackbarStore.show({ 
      text: 'Please fill all required fields marked with an asterisk (*).', 
      color: 'error', 
      timeout: 3000 
    });
    return;
  }
  const requestedDateIso = localForm.pricing_selection.requested_start_date
    ? `${localForm.pricing_selection.requested_start_date}T00:00:00`
    : null;
  const payload: SaveCmacRegistrationPayload = {
    requestId: props.requestId,
    locationType: localForm.customer_info.locationType || null,
    customerType: localForm.customer_info.customer_type || null,
    subsidaryInfo: localForm.customer_info.subsidiaries || null,
    requestedStartDate: requestedDateIso,
    requestTermInMonth: localForm.pricing_selection.request_term_in_month ?? null,
    registrationSelection: localForm.pricing_selection.category || null,
  };
  try {
    saving.value = true;
    await saveCmacRegistration(payload);
    snackbarStore.show({ text: 'CMAC Registration saved.', color: 'success', timeout: 3000 });
  } catch (e) {
    console.error('Failed to save CMAC registration', e);
    snackbarStore.show({ text: 'Failed to save CMAC Registration.', color: 'error', timeout: 3000 });
  } finally {
    saving.value = false;
  }
}

function mapApiToLocal(apiData: any) {
  // Customer information
  if (apiData.customer) {
    localForm.customer_info.name = apiData.customer.displayName || apiData.customer.businessName || '';
  }

  // Map location details if available
  if (apiData.location) {
    localForm.customer_info.name = apiData.location.displayName || localForm.customer_info.name;
    localForm.customer_info.address = apiData.location.addressLine1 || '';

    const address2 = apiData.location.addressLine2 || '';
    const address3 = apiData.location.addressLine3 || '';
    localForm.customer_info.address2_3 = [address2, address3].filter(Boolean).join(', ');

    const city = apiData.location.city || '';
    const state = apiData.location.state || '';
    const postal = apiData.location.postalCode || '';
    localForm.customer_info.city_prov_postal = `${city} / ${state} / ${postal}`.trim();
  }
  localForm.customer_info.locationType =  apiData.locationType || apiData?.location?.locationType || null;
  localForm.customer_info.customer_type = apiData.customerType || apiData?.customer?.customerType || '';
  localForm.customer_info.subsidiaries = apiData.subsidaryInfo || '';

  // Pricing selection
  localForm.pricing_selection.category = apiData.registrationSelection || null;
  if (apiData.requestedStartDate) {
    localForm.pricing_selection.requested_start_date = apiData.requestedStartDate.substring(0, 10); // YYYY-MM-DD
  }
  // Requested term (months)
  if (typeof apiData.requestTermInMonth === 'number') {
    localForm.pricing_selection.request_term_in_month = apiData.requestTermInMonth;
  }

  // Channel info
  localForm.channel_info.branch_name = apiData.salesBranchName || '';
  localForm.channel_info.rep_name = apiData.salesRepName || '';
}

defineExpose({ getFormData, handleSubmit });

// --- Reactive State ---
const localForm = reactive({
  customer_info: {
    name: '',
    address: '',
    address2_3: '',
    city_prov_postal: '',
    contact_name: '',
    title: '',
    telephone: '',
    fax: '',
    locationType: null as string | null,
    customer_type: '',
    subsidiaries: '',
  },
  pricing_selection: {
    category: '' as string | null, // Can be 'S1', 'SA', etc.
    requested_start_date: null as string | null,
    request_term_in_month: null as number | null,
  },
  channel_info: {
    branch_name: '',
    rep_name: '',
    approved_by: '',
    signature: '',
    title: '',
    date: null as string | null,
  },
});

// Initialize localForm with prop value and handle legacy structures
if (props.modelValue) {
  Object.assign(localForm, props.modelValue);
  // Simple migration from old data structure to new one
  if (props.modelValue.sbi_selection?.category) {
    localForm.pricing_selection.category = props.modelValue.sbi_selection.category;
  } else if (props.modelValue.sas_selection?.category) {
    localForm.pricing_selection.category = props.modelValue.sas_selection.category;
  }
}

// Watch for changes and emit upwards
watch(
  localForm,
  (val) => {
    // Clean up legacy properties before emitting
    const output = { ...val };
    delete (output as any).sbi_selection;
    delete (output as any).sas_selection;
    emit('update:modelValue', output);
  },
  { deep: true }
);

// Determine if overlay should be shown (status < 6)
const showNotApprovedOverlay = computed(() => typeof props.status === 'number' && props.status < 6);

 // Basic form validity: require all mandatory fields
 const isValid = computed(() => {
   const hasName = (localForm.customer_info.name || '').trim().length > 0;
   const hasCategory = !!localForm.pricing_selection.category;
   const hasLocationType = !!localForm.customer_info.locationType;
   const hasCustomerType = (localForm.customer_info.customer_type || '').trim().length > 0;
   const hasSubsidiaryInfo = (localForm.customer_info.subsidiaries || '').trim().length > 0;
   const hasRequestedStartDate = !!localForm.pricing_selection.requested_start_date;
   const hasRequestTermInMonth = !!localForm.pricing_selection.request_term_in_month;
   
   return hasName && hasCategory && hasLocationType && hasCustomerType && 
          hasSubsidiaryInfo && hasRequestedStartDate && hasRequestTermInMonth;
 });

// Auto-fill term from the selected row's default when category changes
watch(
  () => localForm.pricing_selection.category,
  (newVal) => {
    if (!newVal) return;
    const def = Object.prototype.hasOwnProperty.call(defaultTermByCategory, newVal)
      ? defaultTermByCategory[newVal]
      : null;
    // Always reflect the selected row's term (e.g., S1=12, S2/S2Y=24; others Per Approval => null)
    localForm.pricing_selection.request_term_in_month = def;
  }
);
</script>

<template>
  <div>
    <v-tabs v-model="activeTab" grow class="mb-4 canon-tabs" bg-color="canon">
      <v-tab value="form">CMAC Registration</v-tab>
      <v-tab value="preview" :disabled="!props.requestId">Preview</v-tab>
    </v-tabs>

    <v-window v-model="activeTab" class="pnl-window">
      <v-window-item value="form">
        <v-container fluid class="bg-white pa-6 position-relative">
    <v-row justify="space-between" align="center">
      <v-col cols="auto">
        <!-- Canon Logo can be an img tag if available -->
        <h2 class="text-h5">Canon</h2>
      </v-col>
      <v-col cols="auto">
        <v-btn variant="outlined">Export CMAC Registration</v-btn>
      </v-col>
    </v-row>

    <div class="text-center my-4">
      <h1 class="text-h6 font-weight-bold">Canon Major Account Commitment (CMAC)</h1>
      <h2 class="text-subtitle-1 font-weight-bold">Account Registration Form</h2>
    </div>

    <!-- Customer Information -->
    <v-card flat variant="outlined" class="pa-4">
      <v-card-title class="text-body-1 font-weight-bold">Customer Information</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="8" class="view-only">
            <v-text-field v-model="localForm.customer_info.name" label="Name" density="compact" variant="outlined" readonly />
            <v-text-field v-model="localForm.customer_info.address" label="Address" density="compact" variant="outlined" readonly />
            <v-text-field v-model="localForm.customer_info.address2_3" label="Address 2/3" density="compact" variant="outlined" readonly />
            <v-text-field v-model="localForm.customer_info.city_prov_postal" label="City/Prov./Postal Code" density="compact" variant="outlined" readonly />
            <!-- <v-row>
              <v-col cols="6"><v-text-field v-model="localForm.customer_info.contact_name" label="Contact Name" density="compact" variant="outlined" readonly /></v-col>
              <v-col cols="6"><v-text-field v-model="localForm.customer_info.title" label="Title" density="compact" variant="outlined" readonly /></v-col>
              <v-col cols="6"><v-text-field v-model="localForm.customer_info.telephone" label="Telephone #" density="compact" variant="outlined" readonly /></v-col>
              <v-col cols="6"><v-text-field v-model="localForm.customer_info.fax" label="Fax #" density="compact" variant="outlined" readonly /></v-col>
            </v-row> -->
          </v-col>
          <v-col cols="12" md="4">
            <label class="text-body-2">Type of Location: <span class="text-red">*</span></label>
            <v-radio-group v-model="localForm.customer_info.locationType" inline>
              <v-radio label="HQ" value="HQ" density="compact" />
              <v-radio label="Branch" value="Branch" density="compact" />
              <v-radio label="Subsidiary" value="Subsidiary" density="compact" />
            </v-radio-group>
            <v-text-field v-model="localForm.customer_info.customer_type" label="Customer Type *" density="compact" variant="outlined" :rules="[v => !!v || 'Customer Type is required']" required />
            <v-textarea v-model="localForm.customer_info.subsidiaries" :maxlength="500"  label="Subsidiaries *" density="compact" variant="outlined" rows="5" :rules="[v => !!v || 'Subsidiaries information is required']" required />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-alert type="info" variant="tonal" density="compact" class="my-4 text-center font-weight-bold">
      **PLEASE REVIEW YOUR PRICING APPROVAL FOR THE CORRECT CATEGORY**
    </v-alert>

    <!-- Pricing Selection -->
    <v-radio-group v-model="localForm.pricing_selection.category">
      <v-row>
        <v-col cols="12">
          <div class="text-body-1 font-weight-bold">Strategic Pricing Requests Over $1 Million in MSRP (SBI) <span class="text-red">*</span></div>
          <v-table density="compact">
            <thead>
              <tr>
                <th class="font-weight-bold">Category</th>
                <th class="font-weight-bold">MSRP Commitment</th>
                <th class="font-weight-bold">Term</th>
                <th class="font-weight-bold text-left">Registration Selection</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in sbiOptions" :key="item.category">
                <td>{{ item.category }}</td>
                <td>{{ item.commitment }}</td>
                <td>{{ item.term }}</td>
                <td class="center-radio"><div class="radio-wrapper"><v-radio :value="item.category" class="ma-0 pa-0" density="compact"></v-radio></div></td>
              </tr>
            </tbody>
          </v-table>
        </v-col>

        <v-col cols="12">
          <div class="text-body-1 font-weight-bold">Strategic Pricing Requests Over $100,000 and Under $1 Million in MSRP (SAS) <span class="text-red">*</span></div>
           <v-table density="compact">
            <thead>
              <tr>
                <th class="font-weight-bold">Category</th>
                <th class="font-weight-bold">CODE</th>
                <th class="font-weight-bold">MSRP Commitment</th>
                <th class="font-weight-bold">Term</th>
                <th class="font-weight-bold text-left">Registration Selection</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in sasOptions" :key="item.category">
                <td>{{ item.category === 'GA' ? 'Special SAS (Global)**' : item.category === 'SA' ? 'Special SAS' : item.category }}</td>
                <td>{{ item.code }}</td>
                <td>{{ item.commitment }}</td>
                <td>{{ item.term }}</td>
                <td class="center-radio"><div class="radio-wrapper"><v-radio :value="item.category" class="ma-0 pa-0" density="compact"></v-radio></div></td>
              </tr>
            </tbody>
          </v-table>
          <div class="text-red text-caption mt-1">**Global Deals must have a global agreement with Canadian Pricing</div>
        </v-col>

        <v-col cols="12" md="4">
            <v-text-field v-model="localForm.pricing_selection.requested_start_date" label="Requested Start Date *" type="date" density="compact" variant="outlined" hint="YYYY/MM/DD" :rules="[v => !!v || 'Start date is required']" required />
        </v-col>
        <v-col cols="12" md="4">
          <v-text-field
            v-model.number="localForm.pricing_selection.request_term_in_month"
            label="Request Term (Months) *"
            type="number"
            :min="1"
            density="compact"
            variant="outlined"
            :rules="[v => !!v || 'Request term is required']"
            required
          />
        </v-col>
      </v-row>
    </v-radio-group>

    <v-alert type="warning" variant="text" class="my-4 font-weight-bold text-blue-darken-3">
      **Please note that the pricing term is subject to change based on any general announcements regarding LRF's or price increases.**
    </v-alert>

    <!-- Terms and Conditions -->
    <v-card flat class="my-4">
      <v-card-title class="text-body-1 font-weight-bold">Terms and Conditions:</v-card-title>
      <v-card-text class="text-body-2">
        By submitting below, Channel confirms receipt of the CMAC Terms and Conditions, and agrees that all of the terms and conditions of the CMAC program, including the section governing charge backs, are incorporated and made a part of this Registration Form. The CMAC guidelines and this Registration Form, are intended to be the entire agreement between Canon Canada and Channel with respect to Canon Canada's bid support to channel for the customer named herein, shall not be effective until this Registration Form has been signed by both Channel and Canon Canada.
      </v-card-text>
    </v-card>

    <!-- Channel Information -->
    <v-card flat variant="outlined" class="pa-4">
      <v-card-title class="text-body-1 font-weight-bold">Channel Information</v-card-title>
      <v-card-text class="view-only">
        <v-row>
          <v-col cols="12" md="6"><v-text-field v-model="localForm.channel_info.branch_name" label="Branch Name" density="compact" variant="outlined" readonly /></v-col>
          <v-col cols="12" md="6"><v-text-field v-model="localForm.channel_info.rep_name" label="Rep Name" density="compact" variant="outlined" readonly /></v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <div class="text-center font-weight-bold mt-6">
      **PLEASE NOTE, IT TAKES 24-48 HOURS FOR THE REGISTRATION NUMBER TO APPEAR IN THE SYSTEM**
    </div>

    <!-- Submit Button -->
    <v-row class="mt-6" justify="center">
      <v-btn color="primary" :loading="saving" :disabled="!isValid || saving" @click="handleSubmit">
        Save CMAC Registration
      </v-btn>
    </v-row>

    <!-- Overlay when not yet approved -->
  <div v-if="showNotApprovedOverlay" class="not-approved-overlay d-flex align-center justify-center">
      <span class="overlay-text">NOT YET APPROVED</span>
  </div>
        </v-container>
      </v-window-item>
      <v-window-item value="preview">
        <PreviewTab v-if="props.requestId" :request-id="props.requestId" />
        <v-alert v-else type="info" variant="tonal">Request not saved yet. Save to enable preview.</v-alert>
      </v-window-item>
    </v-window>
  </div>
</template>

<style scoped>
.v-table th {
  white-space: nowrap;
}
.position-relative {
  position: relative;
}

.not-approved-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(128, 128, 128, 0.6);
  z-index: 10;
  pointer-events: all;
}

.overlay-text {
  display: block;
  font-size: 6vw; /* responsive large size */
  font-weight: 900;
  color: #ffffff;
  text-shadow: 2px 2px 4px #000000;
  transform: rotate(-25deg);
  user-select: none;
}

.center-radio {
  text-align: center;
  vertical-align: middle;
  padding: 0; /* avoid extra offset from default td padding */
}
.center-radio .radio-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 40px; /* ensure decent click target */
}
.center-radio :deep(.v-selection-control),
.center-radio :deep(.v-selection-control__wrapper) {
  margin: 0;
}

/* Visual treatment for read-only blocks */
.view-only :deep(.v-field) {
  background-color: #f7f7f7;
}
.view-only :deep(.v-input--readonly .v-field) {
  cursor: not-allowed;
}
.view-only :deep(input),
.view-only :deep(textarea) {
  color: rgba(0, 0, 0, 0.8);
}

</style>
