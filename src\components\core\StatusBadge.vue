<template>
  <span :class="badgeClass">{{ statusDescription }}</span>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';
import { DsdRequestStatus, getDsdRequestStatusText } from '@/enums/DsdRequestStatus';

// Props
const props = defineProps<{
  status: DsdRequestStatus | number;
}>();

// Status description
const statusDescription = computed(() => {
  return getDsdRequestStatusText(props.status as DsdRequestStatus);
});

// Badge class per status
const badgeClass = computed(() => {
  const s = props.status as DsdRequestStatus;
  switch (s) {
    case DsdRequestStatus.DRAFT:
      return 'badge-default';
    case DsdRequestStatus.SERVICE_FORM_PENDING:
      return 'badge-service-form';
    case DsdRequestStatus.MANAGER_APPROVAL_PENDING:
      return 'badge-manager';
    case DsdRequestStatus.RL_APPROVAL_PENDING:
      return 'badge-rl';
    case DsdRequestStatus.PD_APPROVAL_PENDING:
      return 'badge-pd';
    case DsdRequestStatus.APPROVAL_PRICING_PENDING:
      return 'badge-approave-pricing';  
    case DsdRequestStatus.SERVICE_APPROVAL_PENDING:
      return 'badge-service';
    case DsdRequestStatus.APPROVED:
    case DsdRequestStatus.STATUS_SERVICE_FORM_APPROVED:
      return 'badge-approved';
    default:
      return 'badge-default';
  }
});
</script>

<style scoped>
/* Light theme colors */
.badge-service-form {
  color: #fff;
  background-color: #1e90ff; /* DodgerBlue */
}

.badge-manager {
  color: #fff;
  background-color: #ff8c00; /* DarkOrange */
}

.badge-rl {
  color: #fff;
  background-color: #6a5acd; /* SlateBlue */
}

.badge-pd {
  color: #fff;
  background-color: #8b0000; /* DarkRed */
}

.badge-approave-pricing {
  color: #fff;
  background-color: #0957a0; /* DarkRed */
}

.badge-service {
  color: #fff;
  background-color: #228b22; /* ForestGreen */
}

.badge-approved {
  color: #fff;
  background-color: #2e8b57; /* SeaGreen */
}

.badge-default {
  color: #fff;
  background-color: gray;
}

/* Dark theme support */
:root.dark .badge-service-form {
  background-color: #4682b4; /* SteelBlue */
}

:root.dark .badge-manager {
  background-color: #ffb84d; /* Softer orange */
  color: #000;
}

:root.dark .badge-rl {
  background-color: #9370db; /* MediumPurple */
}

:root.dark .badge-pd {
  background-color: #cd5c5c; /* IndianRed */
}

:root.dark .badge-service {
  background-color: #32cd32; /* LimeGreen */
}

:root.dark .badge-approved {
  background-color: #20c997; /* Teal green */
}

/* Common styling */
span {
  padding: 0.25em 0.6em;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-block;
}
</style>
