<script setup lang="ts">
/**
 * @file Service Desk page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { getServiceDeskList, type PriceDeskRequest } from '@/services/salesRequestService';
import DataTable from '@/components/common/DataTable.vue';
import StatusBadge from '@/components/core/StatusBadge.vue';
import { useAppStore } from '@/stores/AppStore';
import { useRouter } from 'vue-router';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();

// Add language support
const { t } = useI18n();
const router = useRouter();

// Table data
const loading = ref(false);
const headers = computed(() => [
    { title: 'Request Number', key: 'requestNumber', sortable: true },
    { title: 'Customer', key: 'customer.businessName', sortable: true },
    { title: 'Created By', key: 'createdByName', sortable: true },
    { title: 'PD Analyst', key: 'pdAssignmentName', sortable: true,align: 'center' },
    { title: 'Last Updated', key: 'updatedAt', sortable: true },
    { title: 'Status', key: 'serviceFormStatus', sortable: true },
    { title: t('common.actions'), key: 'actions', sortable: false }
]);

const serviceRequests = ref<PriceDeskRequest[]>([]);

// Handle table actions
const handleAction = ({ action, item }: { action: string; item: any }) => {
    if (action === 'view') {
    router.push({ name: 'pageServiceApproval', params: { id: item.requestId } });

        // Handle edit action
        console.log('Edit service request:', item);
    } else if (action === 'approve') {
        // Handle approve action
        console.log('Approve service request:', item);
    } 
};

// Load data
const fetchData = async () => {
  loading.value = true;
  try {
    serviceRequests.value = await getServiceDeskList();
  } catch (error) {
    console.error('Failed to fetch service desk list:', error);
    // Optionally, show an error message to the user
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
    fetchData();
    // Stop page loader when component is mounted
    appStore.stopPageLoader();
});
</script>

<template>
    <div class="pa-4">
                <h1>Service Desk List</h1>
        
        <DataTable
            :headers="headers"
            :items="serviceRequests"
            :loading="loading"
            @action="handleAction"
        >
            <template v-slot:item.updatedAt="{ item }">
                {{ new Date(item.updatedAt).toLocaleDateString() }}
            </template>
            
            <template v-slot:item.pdAssignmentName="{ item }">
                        {{ item.pdAssignmentName ?? '-' }}
                    </template>
            <template v-slot:item.serviceFormStatus="{ item }">
                <StatusBadge :status="item.serviceFormStatus || 0" />
            </template>

            <template v-slot:item.actions="{ item }">
                <v-menu>
                    <template v-slot:activator="{ props }">
                        <v-btn
                            icon="more_vert"
                            variant="text"
                            size="small"
                            v-bind="props"
                        ></v-btn>
                    </template>
                    <v-list>
                        <v-list-item
                            prepend-icon="visibility"
                            :title="'View Service Approval'"
                            @click="handleAction({ action: 'view', item })"
                        ></v-list-item>
                      
                    </v-list>
                </v-menu>
            </template>
        </DataTable>
    </div>
</template>
