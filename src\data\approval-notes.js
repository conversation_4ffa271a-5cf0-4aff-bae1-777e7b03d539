export const approvalNotes = {
  "ITCG Product": [
    {
      id: 1,
      text: "**Note that Hardware pricing CANNOT be reduced to account for any Synergy Allowance or any other Dealer Allowance**"
    },
    {
      id: 2,
      text: "**ITCG Pricing is valid until December 31, 2024 as pricing is based on ITCG quarterly rebates, you must re-apply for pricing each quarter**"
    },
    {
      id: 3,
      text: "**ITCG units must be shipped and billed before expiry date**"
    },
    {
      id: 4,
      text: "This approval is to acknowledge the ITCG approved pricing but does not confirm inventory availability."
    },
    {
      id: 5,
      text: "To confirm availability, please submit a deployment schedule to product planning team @ <EMAIL>."
    },
    {
      id: 6,
      text: "**Note that ITCG Hardware pricing CANNOT be reduced to account for any Synergy Allowance or any other Dealer Allowance**"
    },
    {
      id: 7,
      text: "This approval is to acknowledge the ITCG approved pricing but does not confirm inventory availability."
    },
    {
      id: 8,
      text: "To confirm availability, please submit a deployment schedule to product planning team @ <EMAIL>."
    }
  ],
  "MSA/LCA": [
    {
      id: 9,
      text: "**Price Increases may occur during the term of the contract and are applicable to this account**"
    },
    {
      id: 10,
      text: "**MSA must follow CMAC rules, $100K in MSRP every 6 months must be attained to continue with CMAC pricing, if not, tiered pricing with SAR and CMAC should be provided to the customer**"
    }
  ],
  "RFP": [
    {
      id: 11,
      text: "**Price Increases may occur during the term of the contract and are applicable to this account**"
    },
    {
      id: 12,
      text: "**Cancellation clause has not been requested or approved and cannot be added after the fact.**"
    }
  ],
  "Substitutions": [
    {
      id: 13,
      text: "**Substitution of the \"Out of Stock Item\" to the \"In Stock Sub'd Item\" Approved until DATE ONLY and Must Bill with item code 845ZZ783**"
    }
  ]
};
